const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const { google } = require('googleapis');
const axios = require('axios');
const { YoutubeTranscript } = require('youtube-transcript');
const he = require('he'); // HTML entity decoder
const fs = require('fs');
const fsPromises = fs.promises;
const transcriptService = require('./services/transcriptService');
const { Document, Packer, Paragraph, TextRun, HeadingLevel } = require('docx');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());

// YouTube API configuration
const YOUTUBE_API_KEY = process.env.YOUTUBE_API_KEY;
const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';

// Log API key status (without revealing the full key)
const apiKeyStatus = YOUTUBE_API_KEY
  ? `API key configured (starts with ${YOUTUBE_API_KEY.substring(0, 5)}...)`
  : 'API key not configured';
console.log(`YouTube API status: ${apiKeyStatus}`);

// Check if frontend build directory exists
const frontendBuildPath = path.join(__dirname, '../frontend/youtube-transcribe/dist');
const frontendExists = fs.existsSync(frontendBuildPath);

// Serve static files from the public directory
const publicPath = path.join(__dirname, 'public');
app.use(express.static(publicPath));
console.log(`Serving static files from: ${publicPath}`);

if (frontendExists) {
  console.log(`Serving frontend static files from: ${frontendBuildPath}`);
  // Serve static files
  app.use(express.static(frontendBuildPath));
} else {
  console.log('Frontend build directory not found. Only API endpoints will be available.');
}

// API Routes
// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Get example videos
app.get('/api/examples', async (req, res) => {
  try {
    const exampleVideosPath = path.join(__dirname, 'data/example-videos.json');
    const exampleVideosData = JSON.parse(await fsPromises.readFile(exampleVideosPath, 'utf8'));
    res.json(exampleVideosData);
  } catch (error) {
    console.error('Error fetching example videos:', error.message);
    res.status(500).json({
      error: 'Failed to fetch example videos',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Get video information
app.get('/api/video/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;

    // Make a direct request to the YouTube API using axios
    const response = await axios.get(`${YOUTUBE_API_BASE_URL}/videos`, {
      params: {
        part: 'snippet,contentDetails',
        id: videoId,
        key: YOUTUBE_API_KEY
      }
    });

    if (!response.data.items || response.data.items.length === 0) {
      return res.status(404).json({ error: 'Video not found' });
    }

    res.json(response.data.items[0]);
  } catch (error) {
    console.error('Error fetching video info:', error.message);

    // Extract error details from YouTube API response
    const youtubeError = error.response?.data?.error;
    const errorMessage = youtubeError?.message || 'Failed to fetch video information';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? JSON.stringify(youtubeError) : undefined
    });
  }
});

// Get video captions/subtitles
app.get('/api/captions/:videoId', async (req, res) => {
  try {
    const { videoId } = req.params;

    // Make a direct request to the YouTube API using axios
    const captionResponse = await axios.get(`${YOUTUBE_API_BASE_URL}/captions`, {
      params: {
        part: 'snippet',
        videoId: videoId,
        key: YOUTUBE_API_KEY
      }
    });

    if (!captionResponse.data.items || captionResponse.data.items.length === 0) {
      return res.status(404).json({ error: 'No captions found for this video' });
    }

    res.json(captionResponse.data.items);
  } catch (error) {
    console.error('Error fetching captions:', error.message);

    // Extract error details from YouTube API response
    const youtubeError = error.response?.data?.error;
    const errorMessage = youtubeError?.message || 'Failed to fetch captions';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? JSON.stringify(youtubeError) : undefined
    });
  }
});

// Helper function to format time in HH:MM:SS format
function formatTime(seconds) {
  // Convert to milliseconds and create a date object
  const date = new Date(seconds * 1000);

  // Extract hours, minutes, seconds
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const secs = date.getUTCSeconds();

  // Format the time string
  return [
    hours > 0 ? hours.toString().padStart(2, '0') : null,
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].filter(Boolean).join(':');
}

// Format time for SRT format (HH:MM:SS,mmm)
function formatSrtTime(seconds) {
  const date = new Date(seconds * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const secs = date.getUTCSeconds().toString().padStart(2, '0');
  const ms = date.getUTCMilliseconds().toString().padStart(3, '0');

  return `${hours}:${minutes}:${secs},${ms}`;
}

// Format time for WebVTT format (HH:MM:SS.mmm)
function formatVttTime(seconds) {
  const date = new Date(seconds * 1000);
  const hours = date.getUTCHours().toString().padStart(2, '0');
  const minutes = date.getUTCMinutes().toString().padStart(2, '0');
  const secs = date.getUTCSeconds().toString().padStart(2, '0');
  const ms = date.getUTCMilliseconds().toString().padStart(3, '0');

  return `${hours}:${minutes}:${secs}.${ms}`;
}

// Convert transcript to plain text format
function convertToTxt(transcript) {
  return transcript.map(item => {
    return `[${item.formattedStart}] ${item.text}`;
  }).join('\n\n');
}

// Convert transcript to SRT format
function convertToSrt(transcript) {
  return transcript.map((item, index) => {
    // Format timestamps for SRT (HH:MM:SS,mmm)
    const startTime = formatSrtTime(item.start);
    const endTime = formatSrtTime(item.end);

    return `${index + 1}\n${startTime} --> ${endTime}\n${item.text}\n`;
  }).join('\n');
}

// Convert transcript to WebVTT format
function convertToVtt(transcript) {
  // WebVTT header
  let vtt = 'WEBVTT\n\n';

  // Add cues
  vtt += transcript.map((item, index) => {
    // Format timestamps for VTT (HH:MM:SS.mmm)
    const startTime = formatVttTime(item.start);
    const endTime = formatVttTime(item.end);

    return `${index + 1}\n${startTime} --> ${endTime}\n${item.text}`;
  }).join('\n\n');

  return vtt;
}

// Convert transcript to Word document format
async function convertToDocx(transcript, videoId, language) {
  // Create a new document
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // Title
        new Paragraph({
          children: [
            new TextRun({
              text: `YouTube Transcript - Video ID: ${videoId}`,
              bold: true,
              size: 32,
            }),
          ],
          heading: HeadingLevel.TITLE,
        }),

        // Language info
        new Paragraph({
          children: [
            new TextRun({
              text: `Language: ${language.toUpperCase()}`,
              italics: true,
              size: 24,
            }),
          ],
        }),

        // Empty line
        new Paragraph({
          children: [new TextRun({ text: "" })],
        }),

        // Transcript content
        ...transcript.map(item =>
          new Paragraph({
            children: [
              new TextRun({
                text: `[${item.formattedStart}] `,
                bold: true,
                color: "0066CC",
              }),
              new TextRun({
                text: item.text,
              }),
            ],
          })
        ),
      ],
    }],
  });

  // Generate the document buffer
  return await Packer.toBuffer(doc);
}

// Download captions in specific format
app.get('/api/captions/:videoId/download', async (req, res) => {
  try {
    const { videoId } = req.params;
    const { format = 'txt', lang = 'en' } = req.query;

    // Use the new transcript service with language support
    const transcriptData = await transcriptService.getTranscript(videoId, lang);

    if (!transcriptData || !transcriptData.transcript || transcriptData.transcript.length === 0) {
      return res.status(404).json({ error: 'No transcript found for this video' });
    }

    const formattedTranscript = transcriptData.transcript;

    // Convert to the requested format
    let content = '';
    let filename = `youtube-transcript-${videoId}`;
    let isBuffer = false;

    switch (format.toLowerCase()) {
      case 'txt':
        content = convertToTxt(formattedTranscript);
        filename += '.txt';
        res.setHeader('Content-Type', 'text/plain');
        break;

      case 'srt':
        content = convertToSrt(formattedTranscript);
        filename += '.srt';
        res.setHeader('Content-Type', 'text/plain');
        break;

      case 'vtt':
        content = convertToVtt(formattedTranscript);
        filename += '.vtt';
        res.setHeader('Content-Type', 'text/vtt');
        break;

      case 'docx':
      case 'word':
        content = await convertToDocx(formattedTranscript, videoId, lang);
        filename += '.docx';
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        isBuffer = true;
        break;

      default:
        return res.status(400).json({ error: 'Unsupported format. Supported formats: txt, srt, vtt, docx' });
    }

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Send the file content (handle both string and buffer content)
    if (isBuffer) {
      res.send(content);
    } else {
      res.send(content);
    }

  } catch (error) {
    console.error('Error downloading captions:', error.message);

    const errorMessage = error.message || 'Failed to download captions';
    const statusCode = error.response?.status || 500;

    res.status(statusCode).json({
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

// Get transcript content
app.get('/api/transcript/:videoId', async (req, res) => {
  try {
    // Add cache-busting headers to prevent browser caching issues
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    const { videoId } = req.params;
    const { lang = 'en' } = req.query;
    const forceFresh = req.query.forceFresh === 'true'; // Only force fresh if explicitly requested

    // Use the new transcript service with fallback methods
    // Pass forceFresh to skip cache when language changes
    const transcript = await transcriptService.getTranscript(videoId, lang, forceFresh);

    // Check if the transcript has an error flag
    if (transcript.isError) {
      // Return a 200 status with the error transcript
      // This allows the frontend to display the error message without crashing
      console.warn(`Returning error transcript for ${videoId}: ${transcript.error}`);
    }

    // Return the transcript
    res.json(transcript);
  } catch (error) {
    console.error('Error fetching transcript:', error.message);

    // Instead of returning a 500 error, return a valid transcript object with an error message
    // This prevents the frontend from crashing
    res.json({
      videoId: req.params.videoId,
      language: req.query.lang || 'en',
      transcript: [
        {
          id: 1,
          start: 0,
          end: 10,
          formattedStart: '00:00',
          formattedEnd: '00:10',
          text: 'An error occurred while fetching the transcript. Please try again later.'
        }
      ],
      error: 'Failed to fetch transcript',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined,
      isError: true
    });
  }
});

// Root path handler - serve index.html for the frontend
app.get('/', (req, res) => {
  if (frontendExists) {
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  } else {
    res.json({
      message: 'YouTube Transcript Generator API',
      endpoints: [
        '/api/health',
        '/api/video/:videoId',
        '/api/captions/:videoId',
        '/api/transcript/:videoId',
        '/api/captions/:videoId/download'
      ]
    });
  }
});

// Simple 404 handler for API routes
app.get('/api/*', (req, res) => {
  res.status(404).json({ error: 'API endpoint not found' });
});

// For all other routes, serve the frontend index.html if it exists
app.get('*', (req, res) => {
  if (frontendExists) {
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
