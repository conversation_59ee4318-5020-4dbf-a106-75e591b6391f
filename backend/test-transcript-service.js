/**
 * Test script for the transcript service
 * 
 * This script tests the transcript service with a sample video ID.
 * It tries all fallback methods and reports the results.
 */

const transcriptService = require('./services/transcriptService');

// Sample video IDs to test
const videoIds = [
  'pqWUuYTcG-o',  // Example video (<PERSON>' Stanford Commencement Speech)
  'arj7oStGLkU',  // Example video (<PERSON> <PERSON>: Inside the mind of a master procrastinator)
  'dQw4w9WgXcQ'   // <PERSON> - Never Gonna Give You Up
];

async function testTranscriptService() {
  console.log('Testing transcript service with fallback methods...\n');
  
  for (const videoId of videoIds) {
    console.log(`Testing video ID: ${videoId}`);
    
    try {
      console.time(`Fetch time for ${videoId}`);
      const transcript = await transcriptService.getTranscript(videoId);
      console.timeEnd(`Fetch time for ${videoId}`);
      
      console.log(`Success! Found transcript with ${transcript.transcript.length} items.`);
      console.log(`First few items:`);
      console.log(transcript.transcript.slice(0, 3));
      console.log('\n');
    } catch (error) {
      console.error(`Error fetching transcript for ${videoId}:`, error.message);
    }
  }
}

// Run the test
testTranscriptService().catch(error => {
  console.error('Unhandled error:', error);
});
