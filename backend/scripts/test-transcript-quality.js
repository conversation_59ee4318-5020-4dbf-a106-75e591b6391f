const fs = require('fs');
const path = require('path');
const transcriptService = require('../services/transcriptService');

// List of example video IDs to test
const exampleVideoIds = [
  'TT81fe2IobI',
  'arj7oStGLkU', 
  'Gv2fzC96Z40',
  'pqWUuYTcG-o',
  'ZrN4bKKMlLU'
];

/**
 * Test transcript quality by checking for common issues
 * @param {Array} transcript - Transcript items to test
 * @param {string} videoId - Video ID for logging
 * @returns {Object} Test results
 */
function testTranscriptQuality(transcript, videoId) {
  const issues = [];
  const warnings = [];
  
  if (!transcript || transcript.length === 0) {
    issues.push('Transcript is empty');
    return { issues, warnings, passed: false };
  }
  
  // Test 1: Check for duplicate consecutive items
  for (let i = 1; i < transcript.length; i++) {
    if (transcript[i].text === transcript[i-1].text) {
      issues.push(`Duplicate consecutive text at item ${i}: "${transcript[i].text}"`);
    }
  }
  
  // Test 2: Check for fragmented sentences (very short items)
  const shortItems = transcript.filter(item => item.text.length < 10);
  if (shortItems.length > transcript.length * 0.3) {
    warnings.push(`High number of short text fragments: ${shortItems.length}/${transcript.length}`);
  }
  
  // Test 3: Check for overlapping text (one item contains another)
  for (let i = 1; i < transcript.length; i++) {
    const current = transcript[i].text;
    const previous = transcript[i-1].text;
    
    if (current.includes(previous) && current !== previous) {
      issues.push(`Item ${i} contains previous item: "${current}" contains "${previous}"`);
    }
    
    if (previous.includes(current) && current !== previous) {
      issues.push(`Previous item ${i-1} contains current item: "${previous}" contains "${current}"`);
    }
  }
  
  // Test 4: Check for proper sentence structure
  let hasProperSentences = false;
  for (const item of transcript) {
    if (item.text.length > 20 && /[.!?]/.test(item.text)) {
      hasProperSentences = true;
      break;
    }
  }
  
  if (!hasProperSentences) {
    warnings.push('No proper sentences found (items with punctuation and reasonable length)');
  }
  
  // Test 5: Check for timestamp consistency
  for (let i = 1; i < transcript.length; i++) {
    if (transcript[i].start < transcript[i-1].start) {
      issues.push(`Timestamp inconsistency at item ${i}: ${transcript[i].start} < ${transcript[i-1].start}`);
    }
  }
  
  // Test 6: Check for reasonable content length
  const totalLength = transcript.reduce((sum, item) => sum + item.text.length, 0);
  if (totalLength < 100) {
    warnings.push(`Very short total content: ${totalLength} characters`);
  }
  
  return {
    issues,
    warnings,
    passed: issues.length === 0,
    totalItems: transcript.length,
    totalLength
  };
}

/**
 * Test all example video transcripts
 */
async function testAllTranscripts() {
  console.log('🧪 Starting comprehensive transcript quality tests...\n');
  
  const results = {};
  let totalPassed = 0;
  let totalFailed = 0;
  
  for (const videoId of exampleVideoIds) {
    console.log(`📹 Testing video: ${videoId}`);
    
    try {
      // Get the transcript (this will use cached version if available)
      const transcriptData = await transcriptService.getTranscript(videoId, 'en');
      
      if (!transcriptData || !transcriptData.transcript) {
        console.log(`❌ Failed to get transcript for ${videoId}`);
        results[videoId] = { error: 'Failed to get transcript' };
        totalFailed++;
        continue;
      }
      
      // Test the transcript quality
      const testResult = testTranscriptQuality(transcriptData.transcript, videoId);
      results[videoId] = testResult;
      
      // Log results
      if (testResult.passed) {
        console.log(`✅ PASSED - ${testResult.totalItems} items, ${testResult.totalLength} chars`);
        totalPassed++;
      } else {
        console.log(`❌ FAILED - ${testResult.issues.length} issues found`);
        testResult.issues.forEach(issue => console.log(`   🔴 ${issue}`));
        totalFailed++;
      }
      
      if (testResult.warnings.length > 0) {
        console.log(`⚠️  Warnings:`);
        testResult.warnings.forEach(warning => console.log(`   🟡 ${warning}`));
      }
      
      // Show sample text
      const sampleText = transcriptData.transcript.slice(0, 3)
        .map(item => item.text)
        .join(' ')
        .substring(0, 100);
      console.log(`   📝 Sample: ${sampleText}...`);
      
    } catch (error) {
      console.log(`❌ Error testing ${videoId}: ${error.message}`);
      results[videoId] = { error: error.message };
      totalFailed++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Summary
  console.log('📊 TEST SUMMARY');
  console.log('================');
  console.log(`✅ Passed: ${totalPassed}`);
  console.log(`❌ Failed: ${totalFailed}`);
  console.log(`📈 Success Rate: ${Math.round((totalPassed / (totalPassed + totalFailed)) * 100)}%`);
  
  // Detailed results
  console.log('\n📋 DETAILED RESULTS');
  console.log('===================');
  for (const [videoId, result] of Object.entries(results)) {
    console.log(`\n${videoId}:`);
    if (result.error) {
      console.log(`  Error: ${result.error}`);
    } else {
      console.log(`  Status: ${result.passed ? 'PASSED' : 'FAILED'}`);
      console.log(`  Items: ${result.totalItems}`);
      console.log(`  Length: ${result.totalLength} chars`);
      console.log(`  Issues: ${result.issues.length}`);
      console.log(`  Warnings: ${result.warnings.length}`);
    }
  }
  
  return results;
}

// Run the tests
if (require.main === module) {
  testAllTranscripts()
    .then((results) => {
      const allPassed = Object.values(results).every(r => !r.error && r.passed);
      console.log(`\n🎯 Overall Result: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
      process.exit(allPassed ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testTranscriptQuality, testAllTranscripts };
