const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const { YoutubeTranscript } = require('youtube-transcript');

// Path to example videos data
const exampleVideosPath = path.join(__dirname, '../data/example-videos.json');
const transcriptsDir = path.join(__dirname, '../data/transcripts');
const tempDir = path.join(__dirname, '../temp');
const cachedTranscriptsPath = path.join(__dirname, '../data/cached-transcripts.js');

// Format time in HH:MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Function to parse VTT timestamp to seconds
function parseVttTimestamp(timestamp) {
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

// Function to parse VTT file
function parseVttFile(filePath) {
  try {
    const content = fsSync.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const transcriptItems = [];
    let currentItem = null;
    let inCue = false;
    let textLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      if (!line || line === 'WEBVTT' || line.startsWith('Kind:') || line.startsWith('Language:')) {
        continue;
      }

      if (line.includes('-->')) {
        if (inCue && currentItem) {
          currentItem.text = textLines.join(' ').trim();
          if (currentItem.text) {
            transcriptItems.push(currentItem);
          }
        }

        inCue = true;
        textLines = [];

        const times = line.split('-->').map(t => t.trim());
        const startTime = parseVttTimestamp(times[0]);
        const endTime = parseVttTimestamp(times[1].split(' ')[0]);

        currentItem = {
          start: startTime,
          end: endTime,
          text: ''
        };
      } else if (inCue) {
        textLines.push(line);
      }
    }

    if (inCue && currentItem) {
      currentItem.text = textLines.join(' ').trim();
      if (currentItem.text) {
        transcriptItems.push(currentItem);
      }
    }

    return cleanTranscriptItems(transcriptItems);
  } catch (error) {
    console.error('Error parsing VTT file:', error);
    return [];
  }
}

// Function to clean transcript items
function cleanTranscriptItems(items) {
  if (!items || items.length === 0) return [];

  const cleanedItems = [];
  let previousText = '';

  for (let i = 0; i < items.length; i++) {
    const item = {...items[i]};

    item.text = item.text
      .replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '')
      .replace(/<\/?c>/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    if (!item.text || item.text === previousText) continue;

    cleanedItems.push(item);
    previousText = item.text;
  }

  // Fix overlapping segments
  for (let i = 0; i < cleanedItems.length - 1; i++) {
    if (cleanedItems[i].end > cleanedItems[i + 1].start) {
      cleanedItems[i].end = cleanedItems[i + 1].start;
    }
  }

  // Renumber and format
  cleanedItems.forEach((item, index) => {
    item.id = index + 1;
    item.formattedStart = formatTime(item.start);
    item.formattedEnd = formatTime(item.end);
  });

  return cleanedItems;
}

async function cacheTranscripts() {
  try {
    // Read example videos data
    const exampleVideosData = JSON.parse(await fs.readFile(exampleVideosPath, 'utf8'));
    const videos = exampleVideosData.videos;

    console.log(`Found ${videos.length} example videos to cache transcripts for.`);

    // Ensure transcripts directory exists
    try {
      await fs.mkdir(transcriptsDir, { recursive: true });
    } catch (err) {
      if (err.code !== 'EEXIST') throw err;
    }

    const cachedTranscripts = {};

    // Process each video
    for (const video of videos) {
      const videoId = video.id;
      console.log(`\nProcessing video: ${video.title} (${videoId})`);

      let transcriptData = null;

      // Method 1: Try to use existing VTT file
      const vttPath = path.join(tempDir, `${videoId}.en.vtt`);
      try {
        await fs.access(vttPath);
        console.log(`Found VTT file for ${videoId}, parsing...`);

        const transcriptItems = parseVttFile(vttPath);
        if (transcriptItems && transcriptItems.length > 0) {
          transcriptData = {
            videoId,
            language: 'en',
            transcript: transcriptItems
          };
          console.log(`Successfully parsed ${transcriptItems.length} items from VTT file`);
        }
      } catch (err) {
        console.log(`No VTT file found for ${videoId}, trying API...`);
      }

      // Method 2: Try to use existing cached file from cache directory
      if (!transcriptData) {
        const cachedPath = path.join(__dirname, '../cache', `${videoId}.json`);
        try {
          await fs.access(cachedPath);
          console.log(`Found cached file for ${videoId}, loading...`);

          const cachedContent = await fs.readFile(cachedPath, 'utf8');
          const cachedData = JSON.parse(cachedContent);

          if (cachedData && cachedData.transcript && cachedData.transcript.length > 0) {
            transcriptData = cachedData;
            console.log(`Successfully loaded ${cachedData.transcript.length} items from cached file`);
          }
        } catch (err) {
          console.log(`No cached file found for ${videoId}, trying API...`);
        }
      }

      // Method 3: If no cached file, try YouTube Transcript API
      if (!transcriptData) {
        try {
          const transcriptList = await YoutubeTranscript.fetchTranscript(videoId, {
            lang: 'en'
          });

          if (transcriptList && transcriptList.length > 0) {
            const formattedTranscript = transcriptList.map((item, index) => {
              const startSeconds = item.offset / 1000;
              const durationSeconds = item.duration / 1000;
              const endSeconds = startSeconds + durationSeconds;

              return {
                id: index + 1,
                start: startSeconds,
                end: endSeconds,
                formattedStart: formatTime(startSeconds),
                formattedEnd: formatTime(endSeconds),
                text: item.text
              };
            });

            transcriptData = {
              videoId,
              language: 'en',
              transcript: formattedTranscript
            };
            console.log(`Successfully fetched ${formattedTranscript.length} items from API`);
          }
        } catch (error) {
          console.error(`Error fetching transcript for ${videoId}:`, error.message);
        }
      }

      // Save transcript data
      if (transcriptData) {
        // Save individual file
        const transcriptPath = path.join(transcriptsDir, `${videoId}.json`);
        await fs.writeFile(transcriptPath, JSON.stringify(transcriptData, null, 2));

        // Add to cached transcripts object
        cachedTranscripts[videoId] = transcriptData;

        console.log(`✅ Cached transcript for ${videoId} (${transcriptData.transcript.length} items)`);
      } else {
        console.error(`❌ Failed to get transcript for ${videoId}`);
      }
    }

    // Update the cached-transcripts.js file
    await updateCachedTranscriptsFile(cachedTranscripts);

    console.log('\n🎉 Transcript caching completed successfully!');
  } catch (error) {
    console.error('Error in cacheTranscripts:', error);
  }
}

// Function to update the cached-transcripts.js file
async function updateCachedTranscriptsFile(cachedTranscripts) {
  try {
    const fileContent = `/**
 * Cached transcripts for example videos
 * This file contains pre-processed transcripts for the example videos
 * to ensure quick loading without having to fetch them from YouTube each time.
 *
 * Generated automatically by scripts/cache-transcripts.js
 */

// Map of video IDs to their cached transcripts
const cachedTranscripts = ${JSON.stringify(cachedTranscripts, null, 2)};

module.exports = cachedTranscripts;
`;

    await fs.writeFile(cachedTranscriptsPath, fileContent);
    console.log('✅ Updated cached-transcripts.js file');
  } catch (error) {
    console.error('Error updating cached-transcripts.js:', error);
  }
}

// Run the script
cacheTranscripts();
