const fs = require('fs').promises;
const path = require('path');
const transcriptService = require('../services/transcriptService');

// List of example video IDs from example-videos.json
const exampleVideoIds = [
  'TT81fe2IobI',
  'arj7oStGLkU', 
  'Gv2fzC96Z40',
  'pqWUuYTcG-o',
  'ZrN4bKKMlLU'
];

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

/**
 * Regenerate cached transcripts using the new VTT processing logic
 */
async function regenerateCachedTranscripts() {
  console.log('🔄 Regenerating cached transcripts with new VTT processing logic...\n');
  
  const cachedTranscripts = {};
  
  for (const videoId of exampleVideoIds) {
    try {
      console.log(`📹 Processing video: ${videoId}`);
      
      // Force fresh download to bypass all caches and use new processing logic
      console.log(`⬇️  Downloading fresh transcript with new processing logic...`);
      const transcript = await transcriptService.getTranscript(videoId, 'en', true);
      
      if (transcript && transcript.transcript && transcript.transcript.length > 0) {
        console.log(`✅ Successfully processed transcript for ${videoId}`);
        console.log(`   - Language: ${transcript.language}`);
        console.log(`   - Items: ${transcript.transcript.length}`);
        console.log(`   - Auto-generated: ${transcript.isAutoGenerated || false}`);
        
        // Verify the transcript has proper content
        const sampleText = transcript.transcript.slice(0, 3).map(item => item.text).join(' ');
        console.log(`   - Sample text: ${sampleText.substring(0, 100)}...`);
        
        // Add to cached transcripts object
        cachedTranscripts[videoId] = {
          videoId: videoId,
          language: transcript.language,
          transcript: transcript.transcript,
          isAutoGenerated: transcript.isAutoGenerated || false,
          originalLanguage: transcript.originalLanguage || 'en'
        };
        
        console.log(`💾 Added ${videoId} to cached transcripts`);
        
      } else {
        console.error(`❌ Failed to process transcript for ${videoId}`);
      }
      
      // Add a small delay between downloads to be respectful
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`❌ Error processing ${videoId}:`, error.message);
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Generate the new cached-transcripts.js file
  console.log('📝 Generating new cached-transcripts.js file...');
  
  const fileContent = `/**
 * Cached transcripts for example videos
 * This file contains pre-processed transcripts for the example videos
 * to ensure quick loading without having to fetch them from YouTube each time.
 *
 * Generated automatically by scripts/regenerate-cached-transcripts.js
 * Using the new VTT processing logic to ensure clean, duplicate-free transcripts.
 */

// Map of video IDs to their cached transcripts
const cachedTranscripts = ${JSON.stringify(cachedTranscripts, null, 2)};

module.exports = cachedTranscripts;
`;

  // Write the new file
  const outputPath = path.join(__dirname, '../data/cached-transcripts.js');
  await fs.writeFile(outputPath, fileContent);
  
  console.log(`✅ Successfully wrote new cached-transcripts.js with ${Object.keys(cachedTranscripts).length} videos`);
  console.log(`📁 File location: ${outputPath}`);
  
  // Summary
  console.log('\n📊 REGENERATION SUMMARY');
  console.log('=======================');
  for (const [videoId, data] of Object.entries(cachedTranscripts)) {
    console.log(`${videoId}: ${data.transcript.length} items (${data.isAutoGenerated ? 'auto' : 'manual'})`);
  }
  
  console.log('\n🎉 Cached transcript regeneration completed successfully!');
  console.log('💡 The example videos will now use the new clean transcripts.');
  
  return cachedTranscripts;
}

// Run the script
if (require.main === module) {
  regenerateCachedTranscripts()
    .then((results) => {
      const successCount = Object.keys(results).length;
      console.log(`\n🎯 Overall Result: Successfully regenerated ${successCount}/${exampleVideoIds.length} cached transcripts`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { regenerateCachedTranscripts };
