<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcript Quality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-test {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        .video-test h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .status.loading {
            background-color: #fff3cd;
            color: #856404;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .transcript-preview {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .timestamp {
            color: #007bff;
            font-weight: bold;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .summary {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Transcript Quality Test</h1>
        <p>This page tests the cleaned transcripts for example videos. Use this to verify that the duplicate text issues have been resolved.</p>
        
        <div class="summary">
            <h3>📊 Test Summary</h3>
            <div id="summary">Click "Test All Videos" to run the tests.</div>
        </div>

        <button class="test-button" onclick="testAllVideos()">🚀 Test All Videos</button>
        <button class="test-button" onclick="clearCache()">🗑️ Clear Cache & Retest</button>

        <div id="video-tests">
            <!-- Video test results will be inserted here -->
        </div>
    </div>

    <script>
        const testVideos = [
            { id: 'pqWUuYTcG-o', title: 'Roger Federer Dartmouth Commencement' },
            { id: 'TT81fe2IobI', title: 'Dunning-Kruger Effect' },
            { id: 'arj7oStGLkU', title: 'Example Video 3' },
            { id: 'Gv2fzC96Z40', title: 'Example Video 4' },
            { id: 'ZrN4bKKMlLU', title: 'Example Video 5' }
        ];

        async function testVideo(videoId, title) {
            const container = document.getElementById(`test-${videoId}`);
            const statusEl = container.querySelector('.status');
            const previewEl = container.querySelector('.transcript-preview');
            
            statusEl.className = 'status loading';
            statusEl.textContent = '⏳ Loading transcript...';
            previewEl.textContent = 'Loading...';

            try {
                // Add cache-busting parameter
                const response = await fetch(`/api/transcript/${videoId}?lang=en&_t=${Date.now()}`);
                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                const transcript = data.transcript;
                
                // Analyze transcript quality
                const analysis = analyzeTranscript(transcript);
                
                // Update status
                if (analysis.duplicates === 0) {
                    statusEl.className = 'status success';
                    statusEl.textContent = `✅ PASSED - ${transcript.length} items, no duplicates`;
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = `⚠️ ${analysis.duplicates} potential duplicates found`;
                }

                // Show transcript preview
                const preview = transcript.slice(0, 10).map(item => 
                    `<span class="timestamp">[${item.formattedStart}]</span> ${item.text}`
                ).join('\n');
                
                previewEl.innerHTML = preview + '\n\n... (showing first 10 items)';

                return analysis;

            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = `❌ ERROR: ${error.message}`;
                previewEl.textContent = 'Failed to load transcript';
                return { duplicates: -1, error: error.message };
            }
        }

        function analyzeTranscript(transcript) {
            let duplicates = 0;
            const seenTexts = new Set();

            for (let i = 0; i < transcript.length; i++) {
                const current = transcript[i];
                
                // Check for exact duplicates
                if (seenTexts.has(current.text)) {
                    duplicates++;
                    continue;
                }
                
                // Check for overlapping content with previous items
                for (let j = Math.max(0, i - 3); j < i; j++) {
                    const prev = transcript[j];
                    if (prev.text.includes(current.text) || current.text.includes(prev.text)) {
                        if (Math.abs(current.text.length - prev.text.length) > 10) {
                            duplicates++;
                            break;
                        }
                    }
                }
                
                seenTexts.add(current.text);
            }

            return { duplicates, total: transcript.length };
        }

        async function testAllVideos() {
            // Create test containers
            const container = document.getElementById('video-tests');
            container.innerHTML = testVideos.map(video => `
                <div class="video-test" id="test-${video.id}">
                    <h3>📹 ${video.title} (${video.id})</h3>
                    <div class="status loading">⏳ Preparing test...</div>
                    <div class="transcript-preview">Ready to test...</div>
                </div>
            `).join('');

            // Run tests
            const results = [];
            for (const video of testVideos) {
                const result = await testVideo(video.id, video.title);
                results.push({ ...video, ...result });
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Update summary
            const passed = results.filter(r => r.duplicates === 0).length;
            const total = results.length;
            const summaryEl = document.getElementById('summary');
            
            summaryEl.innerHTML = `
                <strong>Results: ${passed}/${total} videos passed</strong><br>
                Success Rate: ${Math.round((passed/total) * 100)}%<br>
                <small>Generated at: ${new Date().toLocaleString()}</small>
            `;
        }

        function clearCache() {
            // Clear any cached data and retest
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => caches.delete(name));
                });
            }
            
            // Force reload with cache busting
            window.location.reload(true);
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testAllVideos, 1000);
        });
    </script>
</body>
</html>
