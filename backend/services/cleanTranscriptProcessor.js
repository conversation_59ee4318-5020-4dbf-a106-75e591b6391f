/**
 * Clean Transcript Processor
 * 
 * Built from scratch to solve the specific overlapping text issues found in YouTube transcripts.
 * Based on analysis of actual example video transcripts.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process VTT content and remove overlapping segments
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Clean transcript items
 */
function processVttContent(vttContent, lang = 'en') {
  console.log(`[CleanProcessor] Processing VTT for language: ${lang}`);

  try {
    // Try webvtt parser first
    let cues;
    try {
      const parsed = webvtt.parse(vttContent, { strict: false });
      cues = parsed.cues || [];
    } catch (webvttError) {
      console.log(`[CleanProcessor] WebVTT parser failed, using manual parser`);
      cues = parseVttManually(vttContent);
    }

    if (cues.length === 0) {
      console.log(`[CleanProcessor] No cues found`);
      return [];
    }

    console.log(`[CleanProcessor] Parsed ${cues.length} raw cues`);

    // Convert to basic transcript items
    const rawItems = cues.map((cue, index) => {
      let text = cue.text || '';
      
      // Basic cleaning
      text = he.decode(text);
      text = text.replace(/<[^>]*>/g, ''); // Remove HTML/timing tags
      text = text.replace(/\s+/g, ' ').trim(); // Normalize whitespace
      
      return {
        id: index + 1,
        start: cue.start || 0,
        end: cue.end || 0,
        text: text
      };
    }).filter(item => item.text.length > 0);

    console.log(`[CleanProcessor] Created ${rawItems.length} basic items`);

    // Apply overlap removal algorithm
    const cleanItems = removeOverlappingSegments(rawItems);
    
    // Add formatted timestamps
    const finalItems = cleanItems.map((item, index) => ({
      ...item,
      id: index + 1,
      formattedStart: formatTime(item.start),
      formattedEnd: formatTime(item.end)
    }));

    console.log(`[CleanProcessor] Final result: ${finalItems.length} clean items`);
    return finalItems;

  } catch (error) {
    console.error(`[CleanProcessor] Error: ${error.message}`);
    return [];
  }
}

/**
 * Remove overlapping segments using simple sequential processing
 * @param {Array} items - Raw transcript items
 * @returns {Array} Clean items without overlaps
 */
function removeOverlappingSegments(items) {
  if (items.length === 0) return [];

  console.log(`[CleanProcessor] Starting overlap removal with ${items.length} items`);

  const result = [items[0]]; // Start with first item

  for (let i = 1; i < items.length; i++) {
    const current = items[i];
    const lastItem = result[result.length - 1];

    // Get words for comparison
    const lastWords = lastItem.text.toLowerCase().trim().split(/\s+/);
    const currentWords = current.text.toLowerCase().trim().split(/\s+/);

    // Find overlap: check if current starts with words that end the last segment
    let overlapLength = 0;
    const maxCheck = Math.min(lastWords.length, currentWords.length, 12);

    for (let len = maxCheck; len >= 2; len--) {
      const lastEnd = lastWords.slice(-len);
      const currentStart = currentWords.slice(0, len);

      if (lastEnd.join(' ') === currentStart.join(' ')) {
        overlapLength = len;
        break;
      }
    }

    if (overlapLength >= 2) {
      // Found overlap - merge by extending last segment with non-overlapping part
      const remainingWords = currentWords.slice(overlapLength);

      if (remainingWords.length > 0) {
        // Extend last segment with remaining words
        const mergedText = `${lastItem.text} ${remainingWords.join(' ')}`;
        lastItem.text = mergedText;
        lastItem.end = Math.max(lastItem.end, current.end);

        console.log(`[CleanProcessor] Merged overlap (${overlapLength} words): "${currentWords.slice(0, overlapLength).join(' ')}"`);
      } else {
        // Current segment is entirely overlapping - just extend timing
        lastItem.end = Math.max(lastItem.end, current.end);
        console.log(`[CleanProcessor] Skipped entirely overlapping segment`);
      }
    } else {
      // No significant overlap - add as new segment
      result.push(current);
    }
  }

  console.log(`[CleanProcessor] Overlap removal complete: ${items.length} → ${result.length} items`);

  // Filter out very short segments (likely artifacts)
  const filtered = result.filter(item => {
    const wordCount = item.text.trim().split(/\s+/).length;
    const duration = item.end - item.start;

    // Keep segments that have:
    // - More than 2 words, OR
    // - Duration longer than 1 second, OR
    // - Are the only segment in their time range
    return wordCount > 2 || duration > 1.0;
  });

  console.log(`[CleanProcessor] Filtered short segments: ${result.length} → ${filtered.length} items`);
  return filtered;
}

/**
 * Find overlapping text between two segments
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Overlapping portion
 */
function findTextOverlap(text1, text2) {
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  let bestOverlap = '';

  // Check if text2 starts with end of text1 (most common YouTube pattern)
  for (let i = 2; i <= Math.min(words1.length, words2.length, 10); i++) {
    const end1 = words1.slice(-i).join(' ');
    const start2 = words2.slice(0, i).join(' ');

    if (end1.toLowerCase() === start2.toLowerCase()) {
      if (end1.length > bestOverlap.length) {
        bestOverlap = end1;
      }
    }
  }

  // Also check if text1 starts with end of text2 (reverse case)
  for (let i = 2; i <= Math.min(words1.length, words2.length, 10); i++) {
    const end2 = words2.slice(-i).join(' ');
    const start1 = words1.slice(0, i).join(' ');

    if (end2.toLowerCase() === start1.toLowerCase()) {
      if (end2.length > bestOverlap.length) {
        bestOverlap = end2;
      }
    }
  }

  return bestOverlap;
}

/**
 * Merge two overlapping texts by removing the overlap
 * @param {string} text1 - First text (existing)
 * @param {string} text2 - Second text (current)
 * @param {string} overlap - Overlapping portion
 * @returns {string} Merged text with overlap removed
 */
function mergeOverlappingTexts(text1, text2, overlap) {
  // Find the overlap position in both texts
  const text1Lower = text1.toLowerCase();
  const text2Lower = text2.toLowerCase();
  const overlapLower = overlap.toLowerCase();

  // Most common case: text1 ends with overlap, text2 starts with overlap
  const text1EndsWithOverlap = text1Lower.endsWith(overlapLower);
  const text2StartsWithOverlap = text2Lower.startsWith(overlapLower);

  if (text1EndsWithOverlap && text2StartsWithOverlap) {
    // Remove overlap from beginning of text2 and append to text1
    const remainingText2 = text2.substring(overlap.length).trim();
    return remainingText2 ? `${text1} ${remainingText2}` : text1;
  }

  // Alternative case: text2 starts with overlap but text1 doesn't end with it
  if (text2StartsWithOverlap) {
    const remainingText2 = text2.substring(overlap.length).trim();
    return remainingText2 ? `${text1} ${remainingText2}` : text1;
  }

  // Edge case: overlap is in the middle somewhere
  const overlapIndexInText2 = text2Lower.indexOf(overlapLower);
  if (overlapIndexInText2 >= 0) {
    const beforeOverlap = text2.substring(0, overlapIndexInText2).trim();
    const afterOverlap = text2.substring(overlapIndexInText2 + overlap.length).trim();

    let result = text1;
    if (beforeOverlap && !text1Lower.includes(beforeOverlap.toLowerCase())) {
      result = `${result} ${beforeOverlap}`;
    }
    if (afterOverlap) {
      result = `${result} ${afterOverlap}`;
    }
    return result;
  }

  // If no clear overlap pattern, just concatenate unique parts
  return `${text1} ${text2}`;
}

/**
 * Manual VTT parser for when webvtt library fails
 * @param {string} vttContent - Raw VTT content
 * @returns {Array} Parsed cues
 */
function parseVttManually(vttContent) {
  const lines = vttContent.split('\n');
  const cues = [];
  let i = 0;

  // Skip header
  while (i < lines.length && !lines[i].includes('-->')) {
    i++;
  }

  while (i < lines.length) {
    const line = lines[i].trim();
    
    if (line.includes('-->')) {
      const [startStr, endStr] = line.split('-->').map(s => s.trim().split(' ')[0]);
      const start = parseVttTimestamp(startStr);
      const end = parseVttTimestamp(endStr);
      
      i++;
      const textLines = [];
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) textLines.push(textLine);
        i++;
      }
      
      if (textLines.length > 0) {
        cues.push({
          start: start,
          end: end,
          text: textLines.join(' ')
        });
      }
    } else {
      i++;
    }
  }

  return cues;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timeStr - VTT timestamp
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timeStr) {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
  } else if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseFloat(parts[1]);
  }
  return 0;
}

/**
 * Process SRT content
 * @param {string} srtContent - Raw SRT content
 * @returns {Array} Clean transcript items
 */
function processSrtContent(srtContent) {
  console.log(`[CleanProcessor] Processing SRT content`);

  const blocks = srtContent.trim().split(/\n\s*\n/);
  const items = [];

  for (const block of blocks) {
    const lines = block.trim().split('\n');
    if (lines.length < 3) continue;

    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));
    if (timestampLineIndex === -1) continue;

    const timeLine = lines[timestampLineIndex];
    const [startStr, endStr] = timeLine.split('-->').map(t => t.trim());
    
    const start = parseSrtTimestamp(startStr);
    const end = parseSrtTimestamp(endStr);
    
    const textLines = lines.slice(timestampLineIndex + 1);
    let text = textLines.join(' ');
    
    text = he.decode(text);
    text = text.replace(/<[^>]*>/g, '');
    text = text.replace(/\s+/g, ' ').trim();
    
    if (text) {
      items.push({
        id: items.length + 1,
        start: start,
        end: end,
        formattedStart: formatTime(start),
        formattedEnd: formatTime(end),
        text: text
      });
    }
  }

  console.log(`[CleanProcessor] Created ${items.length} SRT items`);
  return items;
}

/**
 * Parse SRT timestamp to seconds
 * @param {string} timestamp - SRT timestamp
 * @returns {number} Time in seconds
 */
function parseSrtTimestamp(timestamp) {
  timestamp = timestamp.replace(',', '.');
  const parts = timestamp.split(':');
  
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
  } else if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseFloat(parts[1]);
  }
  return 0;
}

/**
 * Format time in HH:MM:SS or MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  processSrtContent,
  formatTime
};
