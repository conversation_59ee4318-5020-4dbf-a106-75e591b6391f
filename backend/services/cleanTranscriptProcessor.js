/**
 * Clean Transcript Processor
 * 
 * Built from scratch to solve the specific overlapping text issues found in YouTube transcripts.
 * Based on analysis of actual example video transcripts.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process VTT content and remove overlapping segments
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Clean transcript items
 */
function processVttContent(vttContent, lang = 'en') {
  console.log(`[CleanProcessor] Processing VTT for language: ${lang}`);

  try {
    // Try webvtt parser first
    let cues;
    try {
      const parsed = webvtt.parse(vttContent, { strict: false });
      cues = parsed.cues || [];
    } catch (webvttError) {
      console.log(`[CleanProcessor] WebVTT parser failed, using manual parser`);
      cues = parseVttManually(vttContent);
    }

    if (cues.length === 0) {
      console.log(`[CleanProcessor] No cues found`);
      return [];
    }

    console.log(`[CleanProcessor] Parsed ${cues.length} raw cues`);

    // Convert to basic transcript items
    const rawItems = cues.map((cue, index) => {
      let text = cue.text || '';
      
      // Basic cleaning
      text = he.decode(text);
      text = text.replace(/<[^>]*>/g, ''); // Remove HTML/timing tags
      text = text.replace(/\s+/g, ' ').trim(); // Normalize whitespace
      
      return {
        id: index + 1,
        start: cue.start || 0,
        end: cue.end || 0,
        text: text
      };
    }).filter(item => item.text.length > 0);

    console.log(`[CleanProcessor] Created ${rawItems.length} basic items`);

    // Apply overlap removal algorithm
    const cleanItems = removeOverlappingSegments(rawItems);
    
    // Add formatted timestamps
    const finalItems = cleanItems.map((item, index) => ({
      ...item,
      id: index + 1,
      formattedStart: formatTime(item.start),
      formattedEnd: formatTime(item.end)
    }));

    console.log(`[CleanProcessor] Final result: ${finalItems.length} clean items`);
    return finalItems;

  } catch (error) {
    console.error(`[CleanProcessor] Error: ${error.message}`);
    return [];
  }
}

/**
 * Remove overlapping segments using smart algorithm
 * @param {Array} items - Raw transcript items
 * @returns {Array} Clean items without overlaps
 */
function removeOverlappingSegments(items) {
  if (items.length === 0) return [];

  const result = [];
  
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    let shouldAdd = true;
    let replaceIndex = -1;

    // Check against recent items for overlaps
    for (let j = Math.max(0, result.length - 5); j < result.length; j++) {
      const existing = result[j];
      
      // Skip if too far apart in time (more than 3 seconds)
      if (Math.abs(current.start - existing.start) > 3) continue;

      const currentText = current.text.toLowerCase().trim();
      const existingText = existing.text.toLowerCase().trim();

      // Case 1: Exact duplicate
      if (currentText === existingText) {
        shouldAdd = false;
        break;
      }

      // Case 2: Current is completely contained in existing
      if (existingText.includes(currentText) && currentText.length > 5) {
        shouldAdd = false;
        break;
      }

      // Case 3: Existing is completely contained in current (replace with longer)
      if (currentText.includes(existingText) && existingText.length > 5) {
        replaceIndex = j;
        break;
      }

      // Case 4: Overlapping segments (YouTube's word-level timing issue)
      const overlap = findTextOverlap(existingText, currentText);
      if (overlap.length > 5) { // Any significant overlap (lowered threshold)
        console.log(`[CleanProcessor] Found overlap: "${overlap}"`);
        console.log(`[CleanProcessor] Between: "${existing.text}"`);
        console.log(`[CleanProcessor] And: "${current.text}"`);

        // Merge the segments by combining unique parts
        const mergedText = mergeOverlappingTexts(existing.text, current.text, overlap);
        if (mergedText && mergedText !== existing.text) {
          console.log(`[CleanProcessor] Merged to: "${mergedText}"`);
          result[j] = {
            ...existing,
            text: mergedText,
            end: Math.max(existing.end, current.end)
          };
          shouldAdd = false;
          break;
        }
      }
    }

    if (shouldAdd) {
      if (replaceIndex >= 0) {
        // Replace existing with current (longer text)
        result[replaceIndex] = current;
      } else {
        // Add as new item
        result.push(current);
      }
    }
  }

  return result;
}

/**
 * Find overlapping text between two segments
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Overlapping portion
 */
function findTextOverlap(text1, text2) {
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  let bestOverlap = '';

  // Check if text2 starts with end of text1 (most common YouTube pattern)
  for (let i = 2; i <= Math.min(words1.length, words2.length, 10); i++) {
    const end1 = words1.slice(-i).join(' ');
    const start2 = words2.slice(0, i).join(' ');

    if (end1.toLowerCase() === start2.toLowerCase()) {
      if (end1.length > bestOverlap.length) {
        bestOverlap = end1;
      }
    }
  }

  // Also check if text1 starts with end of text2 (reverse case)
  for (let i = 2; i <= Math.min(words1.length, words2.length, 10); i++) {
    const end2 = words2.slice(-i).join(' ');
    const start1 = words1.slice(0, i).join(' ');

    if (end2.toLowerCase() === start1.toLowerCase()) {
      if (end2.length > bestOverlap.length) {
        bestOverlap = end2;
      }
    }
  }

  return bestOverlap;
}

/**
 * Merge two overlapping texts by removing the overlap
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @param {string} overlap - Overlapping portion
 * @returns {string} Merged text
 */
function mergeOverlappingTexts(text1, text2, overlap) {
  const text1Lower = text1.toLowerCase();
  const text2Lower = text2.toLowerCase();
  const overlapLower = overlap.toLowerCase();

  // Case 1: text2 starts with the overlap (most common)
  if (text2Lower.startsWith(overlapLower)) {
    const remainingText2 = text2.substring(overlap.length).trim();
    return remainingText2 ? `${text1} ${remainingText2}` : text1;
  }

  // Case 2: text1 ends with the overlap and text2 starts with it
  if (text1Lower.endsWith(overlapLower) && text2Lower.startsWith(overlapLower)) {
    const remainingText2 = text2.substring(overlap.length).trim();
    return remainingText2 ? `${text1} ${remainingText2}` : text1;
  }

  // Case 3: text1 starts with the overlap (reverse case)
  if (text1Lower.startsWith(overlapLower)) {
    const remainingText1 = text1.substring(overlap.length).trim();
    return remainingText1 ? `${text2} ${remainingText1}` : text2;
  }

  return null;
}

/**
 * Manual VTT parser for when webvtt library fails
 * @param {string} vttContent - Raw VTT content
 * @returns {Array} Parsed cues
 */
function parseVttManually(vttContent) {
  const lines = vttContent.split('\n');
  const cues = [];
  let i = 0;

  // Skip header
  while (i < lines.length && !lines[i].includes('-->')) {
    i++;
  }

  while (i < lines.length) {
    const line = lines[i].trim();
    
    if (line.includes('-->')) {
      const [startStr, endStr] = line.split('-->').map(s => s.trim().split(' ')[0]);
      const start = parseVttTimestamp(startStr);
      const end = parseVttTimestamp(endStr);
      
      i++;
      const textLines = [];
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) textLines.push(textLine);
        i++;
      }
      
      if (textLines.length > 0) {
        cues.push({
          start: start,
          end: end,
          text: textLines.join(' ')
        });
      }
    } else {
      i++;
    }
  }

  return cues;
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timeStr - VTT timestamp
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timeStr) {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
  } else if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseFloat(parts[1]);
  }
  return 0;
}

/**
 * Process SRT content
 * @param {string} srtContent - Raw SRT content
 * @returns {Array} Clean transcript items
 */
function processSrtContent(srtContent) {
  console.log(`[CleanProcessor] Processing SRT content`);

  const blocks = srtContent.trim().split(/\n\s*\n/);
  const items = [];

  for (const block of blocks) {
    const lines = block.trim().split('\n');
    if (lines.length < 3) continue;

    const timestampLineIndex = lines.findIndex(line => line.includes('-->'));
    if (timestampLineIndex === -1) continue;

    const timeLine = lines[timestampLineIndex];
    const [startStr, endStr] = timeLine.split('-->').map(t => t.trim());
    
    const start = parseSrtTimestamp(startStr);
    const end = parseSrtTimestamp(endStr);
    
    const textLines = lines.slice(timestampLineIndex + 1);
    let text = textLines.join(' ');
    
    text = he.decode(text);
    text = text.replace(/<[^>]*>/g, '');
    text = text.replace(/\s+/g, ' ').trim();
    
    if (text) {
      items.push({
        id: items.length + 1,
        start: start,
        end: end,
        formattedStart: formatTime(start),
        formattedEnd: formatTime(end),
        text: text
      });
    }
  }

  console.log(`[CleanProcessor] Created ${items.length} SRT items`);
  return items;
}

/**
 * Parse SRT timestamp to seconds
 * @param {string} timestamp - SRT timestamp
 * @returns {number} Time in seconds
 */
function parseSrtTimestamp(timestamp) {
  timestamp = timestamp.replace(',', '.');
  const parts = timestamp.split(':');
  
  if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseFloat(parts[2]);
  } else if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseFloat(parts[1]);
  }
  return 0;
}

/**
 * Format time in HH:MM:SS or MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  processSrtContent,
  formatTime
};
