/**
 * Simple Transcript Processor
 * 
 * Minimal, robust transcript processing that prioritizes accuracy and reliability.
 * Removes all complex text manipulation and focuses on essential operations only.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process VTT content with minimal, conservative approach
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code (for logging only)
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent, lang = 'en') {
  console.log(`[SimpleProcessor] Processing VTT content for language: ${lang}`);

  try {
    // Parse VTT content with very lenient settings
    const parsed = webvtt.parse(vttContent, {
      strict: false,
      meta: false
    });

    if (!parsed || !parsed.cues || parsed.cues.length === 0) {
      console.log('[SimpleProcessor] No cues found in VTT content');
      return [];
    }

    console.log(`[SimpleProcessor] Parsed ${parsed.cues.length} cues from VTT file`);

    // Convert cues to transcript items with minimal processing
    const transcriptItems = [];

    for (let i = 0; i < parsed.cues.length; i++) {
      const cue = parsed.cues[i];
      
      // Basic text cleaning only
      let text = cue.text || '';
      
      // Decode HTML entities
      text = he.decode(text);
      
      // Remove HTML/XML tags (including timing tags)
      text = text.replace(/<[^>]*>/g, '');
      
      // Normalize whitespace
      text = text.replace(/\s+/g, ' ').trim();
      
      // Skip empty segments
      if (!text) continue;
      
      // Create transcript item
      transcriptItems.push({
        id: transcriptItems.length + 1,
        start: cue.start || 0,
        end: cue.end || 0,
        formattedStart: formatTime(cue.start || 0),
        formattedEnd: formatTime(cue.end || 0),
        text: text
      });
    }

    console.log(`[SimpleProcessor] Created ${transcriptItems.length} transcript items`);

    // Apply only essential deduplication
    const dedupedItems = removeExactDuplicates(transcriptItems);
    
    console.log(`[SimpleProcessor] After deduplication: ${dedupedItems.length} transcript items`);

    return dedupedItems;

  } catch (error) {
    console.error(`[SimpleProcessor] Error with webvtt parser: ${error.message}`);
    console.log('[SimpleProcessor] Falling back to manual VTT parsing...');

    // Fallback to manual parsing
    return parseVttManually(vttContent);
  }
}

/**
 * Manual VTT parser as fallback
 * @param {string} vttContent - Raw VTT content
 * @returns {Array} Parsed transcript items
 */
function parseVttManually(vttContent) {
  console.log('[SimpleProcessor] Using manual VTT parser');

  const lines = vttContent.split('\n');
  const transcriptItems = [];
  let i = 0;

  // Skip header lines
  while (i < lines.length && !lines[i].includes('-->')) {
    i++;
  }

  while (i < lines.length) {
    const line = lines[i].trim();

    if (line.includes('-->')) {
      // Parse timestamp line
      const [startStr, endStr] = line.split('-->').map(s => s.trim().split(' ')[0]);
      const start = parseVttTimestamp(startStr);
      const end = parseVttTimestamp(endStr);

      // Collect text lines
      i++;
      const textLines = [];
      while (i < lines.length && lines[i].trim() !== '' && !lines[i].includes('-->')) {
        const textLine = lines[i].trim();
        if (textLine) {
          textLines.push(textLine);
        }
        i++;
      }

      if (textLines.length > 0) {
        let text = textLines.join(' ');

        // Basic cleaning
        text = he.decode(text);
        text = text.replace(/<[^>]*>/g, '');
        text = text.replace(/\s+/g, ' ').trim();

        if (text) {
          transcriptItems.push({
            id: transcriptItems.length + 1,
            start: start,
            end: end,
            formattedStart: formatTime(start),
            formattedEnd: formatTime(end),
            text: text
          });
        }
      }
    } else {
      i++;
    }
  }

  console.log(`[SimpleProcessor] Manual parser created ${transcriptItems.length} items`);

  // Apply deduplication
  return removeExactDuplicates(transcriptItems);
}

/**
 * Parse VTT timestamp to seconds
 * @param {string} timeStr - VTT timestamp
 * @returns {number} Time in seconds
 */
function parseVttTimestamp(timeStr) {
  const parts = timeStr.split(':');
  if (parts.length === 3) {
    const [hours, minutes, seconds] = parts;
    return parseInt(hours) * 3600 + parseInt(minutes) * 60 + parseFloat(seconds);
  } else if (parts.length === 2) {
    const [minutes, seconds] = parts;
    return parseInt(minutes) * 60 + parseFloat(seconds);
  }
  return 0;
}

/**
 * Remove only exact duplicate segments that are very close in time
 * @param {Array} items - Transcript items
 * @returns {Array} Deduplicated items
 */
function removeExactDuplicates(items) {
  if (!items || items.length === 0) return [];

  const result = [];
  
  for (const item of items) {
    let isDuplicate = false;
    
    // Only check the last few items for exact duplicates
    for (let i = Math.max(0, result.length - 3); i < result.length; i++) {
      const existing = result[i];
      
      // Check for exact text match within 1 second
      if (existing.text === item.text && Math.abs(item.start - existing.start) <= 1.0) {
        // Extend the timing of the existing item if needed
        existing.end = Math.max(existing.end, item.end);
        existing.formattedEnd = formatTime(existing.end);
        isDuplicate = true;
        break;
      }
    }
    
    if (!isDuplicate) {
      result.push(item);
    }
  }
  
  // Renumber IDs
  for (let i = 0; i < result.length; i++) {
    result[i].id = i + 1;
  }
  
  return result;
}

/**
 * Format time in HH:MM:SS or MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

/**
 * Process SRT content with minimal approach
 * @param {string} srtContent - Raw SRT file content
 * @returns {Array} Processed transcript items
 */
function processSrtContent(srtContent) {
  console.log('[SimpleProcessor] Processing SRT content');

  try {
    const blocks = srtContent.trim().split(/\n\s*\n/);
    const transcriptItems = [];

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      
      if (lines.length < 3) continue;
      
      // Parse timing line (second line)
      const timingLine = lines[1];
      const timingMatch = timingLine.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})\s*-->\s*(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
      
      if (!timingMatch) continue;
      
      // Convert to seconds
      const startTime = parseInt(timingMatch[1]) * 3600 + parseInt(timingMatch[2]) * 60 + parseInt(timingMatch[3]) + parseInt(timingMatch[4]) / 1000;
      const endTime = parseInt(timingMatch[5]) * 3600 + parseInt(timingMatch[6]) * 60 + parseInt(timingMatch[7]) + parseInt(timingMatch[8]) / 1000;
      
      // Get text (lines 3 and beyond)
      let text = lines.slice(2).join(' ');
      
      // Basic cleaning
      text = he.decode(text);
      text = text.replace(/<[^>]*>/g, '');
      text = text.replace(/\s+/g, ' ').trim();
      
      if (!text) continue;
      
      transcriptItems.push({
        id: transcriptItems.length + 1,
        start: startTime,
        end: endTime,
        formattedStart: formatTime(startTime),
        formattedEnd: formatTime(endTime),
        text: text
      });
    }

    console.log(`[SimpleProcessor] Created ${transcriptItems.length} SRT transcript items`);
    return transcriptItems;

  } catch (error) {
    console.error(`[SimpleProcessor] Error processing SRT content: ${error.message}`);
    return [];
  }
}

module.exports = {
  processVttContent,
  processSrtContent,
  formatTime
};
