/**
 * Transcript Service
 *
 * Handles fetching and processing YouTube transcripts using multiple methods.
 */

const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { YoutubeTranscript } = require('youtube-transcript');
const util = require('util');
const axios = require('axios');
const cheerio = require('cheerio');
const he = require('he');

// Import the clean transcript processor
const cleanProcessor = require('./cleanTranscriptProcessor');

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Cache directory
const CACHE_DIR = path.join(__dirname, '../cache');

// Known English videos for language detection
const knownEnglishVideos = ['pqWUuYTcG-o', 'arj7oStGLkU', 'TT81fe2IobI', 'KpVPST_P4W8', 'Gv2fzC96Z40', 'ZrN4bKKMlLU'];

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

/**
 * Get transcript from cache
 * @param {string} videoId - YouTube video ID
 * @returns {Promise<object|null>} Cached transcript data or null if not found
 */
async function getFromCache(videoId) {
  try {
    // Create cache directory if it doesn't exist
    await fs.mkdir(CACHE_DIR, { recursive: true });

    const cachePath = path.join(CACHE_DIR, `${videoId}.json`);

    // Check if cache file exists
    try {
      await fs.access(cachePath);
    } catch (error) {
      // File doesn't exist
      return null;
    }

    // Read and parse cache file
    const cacheData = await fs.readFile(cachePath, 'utf8');
    return JSON.parse(cacheData);
  } catch (error) {
    console.error(`Error reading from cache for ${videoId}:`, error.message);
    return null;
  }
}

/**
 * Save transcript to cache
 * @param {string} videoId - YouTube video ID
 * @param {object} transcriptData - Transcript data to cache
 * @returns {Promise<boolean>} Success status
 */
async function saveToCache(videoId, transcriptData) {
  try {
    // Create cache directory if it doesn't exist
    await fs.mkdir(CACHE_DIR, { recursive: true });

    const cachePath = path.join(CACHE_DIR, `${videoId}.json`);

    // Write transcript data to cache file
    await fs.writeFile(cachePath, JSON.stringify(transcriptData, null, 2));

    console.log(`Saved transcript for ${videoId} to cache`);
    return true;
  } catch (error) {
    console.error(`Error saving to cache for ${videoId}:`, error.message);
    return false;
  }
}

/**
 * Decode HTML entities in text
 * @param {string} text - Text with HTML entities
 * @returns {string} Decoded text
 */
function decodeHtmlEntities(text) {
  if (!text) return '';

  // First use the 'he' library for standard HTML entities
  let decodedText = he.decode(text);

  // Handle common YouTube-specific entities and other escaped characters
  // that might not be properly decoded by the he library
  decodedText = decodedText
    // Common HTML entities
    .replace(/&#39;/g, "'")
    .replace(/&amp;#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&amp;quot;/g, '"')
    .replace(/&amp;amp;/g, '&')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;lt;/g, '<')
    .replace(/&amp;gt;/g, '>')
    // Unicode characters
    .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Other common escaped characters
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\r/g, '\r')
    .replace(/\\\\/g, '\\')
    .replace(/\\'/g, "'")
    .replace(/\\"/g, '"');

  return decodedText;
}

/**
 * Remove translator information from transcript text
 * @param {string} text - The transcript text
 * @returns {string} Cleaned text without translator information
 */
function removeTranslatorInfo(text) {
  if (!text) return '';

  // First decode any HTML entities in the text
  let cleanedText = decodeHtmlEntities(text);

  // Common patterns for translator information
  const patterns = [
    /^Translator:\s*[^.]+(\n|$)/i,
    /^Translated by\s*[^.]+(\n|$)/i,
    /^Translation by\s*[^.]+(\n|$)/i,
    /^Reviewer:\s*[^.]+(\n|$)/i,
    /^Reviewed by\s*[^.]+(\n|$)/i,
    /^Translated and reviewed by\s*[^.]+(\n|$)/i,
    /^Transcribed by\s*[^.]+(\n|$)/i,
    /^Transcription by\s*[^.]+(\n|$)/i,
    /^Subtitles by\s*[^.]+(\n|$)/i,
    /^Captions by\s*[^.]+(\n|$)/i
  ];

  // Apply each pattern
  patterns.forEach(pattern => {
    cleanedText = cleanedText.replace(pattern, '');
  });

  // Remove lines that contain both "Translator" and "Reviewer"
  cleanedText = cleanedText.replace(/^.*Translator.*Reviewer.*(\n|$)/i, '');

  // Remove any remaining translator-related lines in the first 2 lines
  const lines = cleanedText.split('\n');
  if (lines.length > 0 && (
      lines[0].includes('Translator') ||
      lines[0].includes('translated') ||
      lines[0].includes('Reviewer') ||
      lines[0].includes('reviewed'))) {
    lines.shift();
  }
  if (lines.length > 0 && (
      lines[0].includes('Translator') ||
      lines[0].includes('translated') ||
      lines[0].includes('Reviewer') ||
      lines[0].includes('reviewed'))) {
    lines.shift();
  }

  return lines.join('\n');
}

/**
 * Format transcript data for consistent output
 * @param {Array} transcriptItems - Raw transcript items
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @param {boolean} isAutoGenerated - Whether the subtitles are auto-generated
 * @returns {object} Formatted transcript data
 */
function formatTranscriptData(transcriptItems, videoId, lang, isAutoGenerated = false) {
  if (!transcriptItems || transcriptItems.length === 0) {
    return {
      videoId,
      language: lang,
      transcript: []
    };
  }

  // Check if this is a known English video (for default language detection)
  const isKnownEnglishVideo = knownEnglishVideos.includes(videoId);

  // Set the original language (but don't force it)
  const originalLanguage = isKnownEnglishVideo ? 'en' : null;

  // Check if this is a non-English transcript
  console.log(`Checking language for transcript formatting: ${lang}`);
  // Force non-English processing for German language
  const isNonEnglish = lang !== 'en' && lang !== 'en-US' && lang !== 'en-orig';
  console.log(`Using ${isNonEnglish ? 'non-English' : 'English'} transcript processor`);

  // Process based on language
  // Ensure isAutoGenerated is defined with a default value
  const autoGenerated = isAutoGenerated || false;

  let result;
  if (isNonEnglish) {
    result = formatNonEnglishTranscript(transcriptItems, videoId, lang, autoGenerated);
  } else {
    result = formatEnglishTranscript(transcriptItems, videoId, lang, autoGenerated);
  }

  // Add original language information if available
  if (originalLanguage) {
    result.originalLanguage = originalLanguage;
  }

  return result;
}

/**
 * Format English transcript data
 * @param {Array} transcriptItems - Raw transcript items
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @param {boolean} isAutoGenerated - Whether the subtitles are auto-generated
 * @returns {object} Formatted transcript data
 */
function formatEnglishTranscript(transcriptItems, videoId, lang, isAutoGenerated = false) {
  // Step 1: Convert the raw transcript items to a standard format
  const standardItems = transcriptItems.map((item, index) => {
    // Convert from milliseconds to seconds if needed
    const start = item.start !== undefined ? item.start : item.offset / 1000;
    const end = item.end !== undefined ? item.end : (item.offset / 1000 + item.duration / 1000);

    // Decode HTML entities in the text
    let processedText = decodeHtmlEntities(item.text);

    // Remove translator information
    processedText = removeTranslatorInfo(processedText);

    return {
      id: index + 1,
      start: start,
      end: end,
      formattedStart: formatTime(start),
      formattedEnd: formatTime(end),
      text: processedText
    };
  });

  // Step 2: Clean up the transcript items
  cleanupTranscriptItems(standardItems);

  return {
    videoId,
    language: lang,
    transcript: standardItems,
    isAutoGenerated: isAutoGenerated || false
  };
}

/**
 * Format non-English transcript data using specialized processor
 * @param {Array} transcriptItems - Raw transcript items
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @param {boolean} isAutoGenerated - Whether the subtitles are auto-generated
 * @returns {object} Formatted transcript data
 */
function formatNonEnglishTranscript(transcriptItems, videoId, lang, isAutoGenerated = false) {
  console.log(`Processing non-English transcript (${lang}) with ${transcriptItems.length} items`);

  // Step 1: Convert the raw transcript items to a standard format
  const standardItems = transcriptItems.map(item => {
    // Convert from milliseconds to seconds if needed
    const start = item.start !== undefined ? item.start : item.offset / 1000;
    const end = item.end !== undefined ? item.end : (item.offset / 1000 + item.duration / 1000);

    // Decode HTML entities in the text
    let processedText = decodeHtmlEntities(item.text);

    // Remove timestamp tags from text (common in non-English transcripts)
    processedText = processedText.replace(/<\d+:\d+:\d+\.\d+>|<\d+:\d+\.\d+>/g, '');

    return {
      start: start,
      end: end,
      text: processedText
    };
  });

  console.log(`After initial processing: ${standardItems.length} items`);

  // Step 2: Add formatted timestamps using clean processor
  const formattedItems = standardItems.map((item, index) => {
    return {
      ...item,
      id: index + 1,
      formattedStart: cleanProcessor.formatTime(item.start),
      formattedEnd: cleanProcessor.formatTime(item.end)
    };
  });

  console.log(`After formatting: ${formattedItems.length} items`);

  return {
    videoId,
    language: lang,
    transcript: formattedItems,
    isAutoGenerated: isAutoGenerated || false
  };
}

/**
 * Clean up transcript items by fixing punctuation, removing duplicates, etc.
 * @param {Array} items - Transcript items to clean up
 */
function cleanupTranscriptItems(items) {
  if (!items || items.length === 0) return;

  // First pass: ensure spaces after punctuation marks
  for (let i = 0; i < items.length; i++) {
    if (!items[i].text) continue;

    // Add space after punctuation marks if not already present and not at the end of text
    items[i].text = items[i].text.replace(/([.!?,;:])([^\s])/g, '$1 $2');
  }

  // Second pass: move punctuation marks at the beginning of segments to the end of previous segments
  for (let i = 1; i < items.length; i++) {
    if (!items[i].text || !items[i-1].text) continue;

    // Check if the current segment starts with punctuation
    const punctuationMatch = items[i].text.match(/^([.!?,;:]+)(\s*)(.*)/);

    if (punctuationMatch) {
      const [, punctuation, spaces, remainingText] = punctuationMatch;

      // Only proceed if there's actual text after the punctuation
      if (remainingText && remainingText.trim().length > 0) {
        // Move the punctuation to the end of the previous segment
        // If the previous segment already ends with punctuation, don't add more
        if (!/[.!?,;:]$/.test(items[i-1].text)) {
          items[i-1].text = items[i-1].text + punctuation;
        }

        // Update the current segment without the leading punctuation
        items[i].text = remainingText;
      }
    }
  }

  // Third pass: ensure no double spaces and proper spacing after punctuation
  for (let i = 0; i < items.length; i++) {
    if (!items[i].text) continue;

    // Replace multiple spaces with a single space
    items[i].text = items[i].text.replace(/\s+/g, ' ').trim();
  }

  // Third pass: only remove exact duplicates with identical timestamps
  const uniqueItems = [];

  for (let i = 0; i < items.length; i++) {
    if (!items[i].text || items[i].text.trim() === '') continue;

    // Skip only if this item is exactly identical to another one with very close timestamps
    let isDuplicate = false;
    for (const existingItem of uniqueItems) {
      if (items[i].text === existingItem.text &&
          Math.abs(items[i].start - existingItem.start) < 0.1) {
        isDuplicate = true;
        console.log(`Removing duplicate segment: "${items[i].text}" at ${items[i].start}s`);
        break;
      }
    }

    if (!isDuplicate) {
      uniqueItems.push(items[i]);
    }
  }

  // Replace the original array with our deduplicated one
  items.length = 0;
  uniqueItems.forEach(item => items.push(item));

  // Renumber the IDs
  for (let i = 0; i < items.length; i++) {
    items[i].id = i + 1;
  }
}

/**
 * Parse VTT subtitle file
 * @param {string} vttContent - Content of the VTT file
 * @param {string} lang - Language code
 * @returns {object} Formatted transcript data
 */
function parseVttSubtitles(vttContent, lang = 'en') {
  console.log(`Parsing VTT subtitles for language: ${lang}`);

  // Use the clean processor to remove overlapping segments
  const transcriptItems = cleanProcessor.processVttContent(vttContent, lang);

  // Return in the final format
  return {
    transcript: transcriptItems,
    isProcessed: true // Flag to indicate this is already processed
  };
}

/**
 * Parse SRT subtitle file
 * @param {string} srtContent - Content of the SRT file
 * @returns {Array} Array of transcript items
 */
function parseSrtSubtitles(srtContent) {
  // Use the clean processor for SRT files
  return cleanProcessor.processSrtContent(srtContent);
}

/**
 * Parse SRT timestamp to seconds
 * @param {string} timestamp - SRT timestamp (HH:MM:SS,mmm)
 * @returns {number} Time in seconds
 */
function parseSrtTimestamp(timestamp) {
  // Replace comma with period for milliseconds
  timestamp = timestamp.replace(',', '.');

  // Handle both HH:MM:SS.mmm and MM:SS.mmm formats
  const parts = timestamp.split(':');
  let hours = 0, minutes = 0, seconds = 0;

  if (parts.length === 3) {
    hours = parseInt(parts[0]);
    minutes = parseInt(parts[1]);
    seconds = parseFloat(parts[2]);
  } else if (parts.length === 2) {
    minutes = parseInt(parts[0]);
    seconds = parseFloat(parts[1]);
  }

  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Fetch transcript using youtube-transcript package
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @returns {Promise<object|null>} Transcript data or null if not found
 */
async function fetchWithYoutubeTranscript(videoId, lang) {
  try {
    console.log(`Fetching transcript for ${videoId} with language ${lang} using youtube-transcript...`);

    // Verify that YoutubeTranscript is properly imported and has the fetchTranscript method
    if (!YoutubeTranscript || typeof YoutubeTranscript.fetchTranscript !== 'function') {
      console.error('YoutubeTranscript.fetchTranscript is not a function or YoutubeTranscript is not properly imported');
      return null;
    }

    // Note: youtube-transcript API doesn't support language selection directly
    // We'll try to fetch available transcripts and filter by language later

    // Fetch transcript using youtube-transcript package
    // The API only accepts videoId, not options, so we'll handle language selection later
    const transcriptItems = await YoutubeTranscript.fetchTranscript(videoId);

    // Check if the result is empty or not an array
    if (!transcriptItems || !Array.isArray(transcriptItems)) {
      console.log(`Invalid response from youtube-transcript for ${videoId}: ${JSON.stringify(transcriptItems)}`);
      return null;
    }

    // Check if the array is empty
    if (transcriptItems.length === 0) {
      console.log(`Empty transcript array returned from youtube-transcript for ${videoId}`);
      return null;
    }

    console.log(`Found ${transcriptItems.length} transcript items with youtube-transcript`);

    // Check if the transcript contains Arabic text (which seems to be a common issue)
    const containsArabic = transcriptItems.some(item =>
      item.text && /[\u0600-\u06FF]/.test(item.text)
    );

    // If the requested language is not Arabic but we got Arabic text, skip this method
    if (lang !== 'ar' && containsArabic) {
      console.log('Detected Arabic transcript but requested language is not Arabic. Skipping this method.');
      return null;
    }

    // For Steve Jobs video (pqWUuYTcG-o), always ensure we're not getting Arabic
    if (videoId === 'pqWUuYTcG-o' && containsArabic) {
      console.log('Steve Jobs video detected with Arabic transcript. Skipping this method to ensure English transcript.');
      return null;
    }

    // Determine if these are auto-generated subtitles
    const isAutoGenerated = true; // youtube-transcript always returns auto-generated subtitles

    // Format the transcript data
    return formatTranscriptData(transcriptItems, videoId, lang, isAutoGenerated);
  } catch (error) {
    console.error(`Error fetching transcript with youtube-transcript: ${error.message}`);
    // Log the full error for debugging
    console.error(`Full error: ${error.stack || JSON.stringify(error)}`);
    return null;
  }
}

/**
 * Fetch transcript using yt-dlp
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @returns {Promise<object|null>} Transcript data or null if not found
 */
async function fetchWithYtDlp(videoId, lang) {
  const tempDir = path.join(__dirname, '../temp');
  const workingDir = process.cwd(); // Get current working directory

  try {
    // Create temp directory if it doesn't exist
    await fs.mkdir(tempDir, { recursive: true });

    console.log(`Fetching transcript for ${videoId} with language ${lang} using yt-dlp...`);
    console.log(`Current working directory: ${workingDir}`);
    console.log(`Temp directory: ${tempDir}`);

    // Allow any language to be requested for any video

    // Try to get captions in the requested language
    // For non-English languages, we need to be more specific
    const isNonEnglish = lang !== 'en' && lang !== 'en-US' && lang !== 'en-orig';

    // Use absolute paths to avoid working directory issues
    const outputTemplate = path.resolve(tempDir, `${videoId}.%(ext)s`);

    // Use the updated yt-dlp version for better YouTube compatibility
    const ytDlpPath = '/Users/<USER>/Library/Python/3.10/bin/yt-dlp';

    let command;
    if (isNonEnglish) {
      // For non-English, prioritize auto-generated captions in the target language
      command = `${ytDlpPath} --write-auto-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${outputTemplate}"`;
    } else {
      // For English, try both manual and auto-generated captions
      command = `${ytDlpPath} --write-auto-sub --write-sub --sub-lang ${lang} --skip-download https://www.youtube.com/watch?v=${videoId} -o "${outputTemplate}"`;
    }

    console.log(`Executing yt-dlp command: ${command}`);

    // First, clean up any existing subtitle files for this video to avoid conflicts
    try {
      const existingFiles = await fs.readdir(tempDir);
      for (const file of existingFiles) {
        if (file.startsWith(videoId) && (file.endsWith('.vtt') || file.endsWith('.srt'))) {
          await fs.unlink(path.join(tempDir, file));
          console.log(`Deleted existing subtitle file: ${file}`);
        }
      }
    } catch (cleanupError) {
      console.error(`Error cleaning up existing subtitle files: ${cleanupError.message}`);
      // Continue with the process even if cleanup fails
    }

    try {
      const { stdout, stderr } = await execPromise(command);
      console.log(`yt-dlp stdout: ${stdout}`);

      if (stderr && stderr.trim() !== '') {
        console.log(`yt-dlp stderr: ${stderr}`);
        // Only treat as error if it contains ERROR and not just WARNING
        if (stderr.includes('ERROR:') && !stderr.includes('WARNING:')) {
          throw new Error(`Command failed: ${command}\n\n${stderr}`);
        }
      }
    } catch (execError) {
      console.error(`Error executing yt-dlp command: ${execError.message}`);

      // Try a more aggressive approach with --force-overwrites
      try {
        console.log('Trying again with --force-overwrites...');
        const retryCommand = command.replace('--skip-download', '--skip-download --force-overwrites');
        const { stdout, stderr } = await execPromise(retryCommand);
        console.log(`Retry yt-dlp stdout: ${stdout}`);

        if (stderr && stderr.trim() !== '') {
          console.log(`Retry yt-dlp stderr: ${stderr}`);
        }
      } catch (retryError) {
        console.error(`Retry also failed: ${retryError.message}`);
        // Continue to try to find existing subtitle files
      }
    }

    // Find the subtitle file
    const files = await fs.readdir(tempDir);
    console.log(`Files in temp directory: ${files.join(', ')}`);

    // First try to find a subtitle file in the requested language
    let subtitleFile = files.find(file =>
      file.startsWith(videoId) &&
      file.includes(`.${lang}.`) &&
      (file.endsWith('.vtt') || file.endsWith('.srt'))
    );

    // If not found, try to find any subtitle file for this video
    if (!subtitleFile) {
      subtitleFile = files.find(file =>
        file.startsWith(videoId) &&
        (file.endsWith('.vtt') || file.endsWith('.srt'))
      );
    }

    if (!subtitleFile) {
      console.log('No subtitle file found in temp directory');

      // Check if the file was created in the current working directory instead
      try {
        const cwdFiles = await fs.readdir(workingDir);
        console.log(`Files in current working directory: ${cwdFiles.join(', ')}`);

        subtitleFile = cwdFiles.find(file =>
          file.startsWith(videoId) &&
          (file.endsWith('.vtt') || file.endsWith('.srt'))
        );

        if (subtitleFile) {
          console.log(`Found subtitle file in current working directory: ${subtitleFile}`);

          // Copy the file to the temp directory
          const sourcePath = path.join(workingDir, subtitleFile);
          const destPath = path.join(tempDir, subtitleFile);
          await fs.copyFile(sourcePath, destPath);
          console.log(`Copied subtitle file from ${sourcePath} to ${destPath}`);

          // Continue processing with the file in the temp directory
        } else {
          console.log('No subtitle file found in current working directory either');
          return null;
        }
      } catch (cwdError) {
        console.error(`Error checking current working directory: ${cwdError.message}`);
        return null;
      }
    }

    console.log(`Found subtitle file: ${subtitleFile}`);

    // Read the subtitle file
    const subtitlePath = path.join(tempDir, subtitleFile);
    const subtitleContent = await fs.readFile(subtitlePath, 'utf8');

    // Parse based on file extension
    let result;

    if (subtitleFile.endsWith('.vtt')) {
      result = parseVttSubtitles(subtitleContent, lang);

      // Check if this is already processed data
      if (result.isProcessed) {
        // Data is already fully processed, just add metadata
        const isAutoGenerated = subtitleFile.includes('.auto.');
        console.log(`Subtitle file is ${isAutoGenerated ? 'auto-generated' : 'manual'}`);

        return {
          videoId,
          language: lang,
          transcript: result.transcript,
          isAutoGenerated: !isAutoGenerated // Manual subtitles are NOT auto-generated
        };
      } else {
        // Fallback for old format (shouldn't happen with current code)
        const transcriptItems = result;
        const isAutoGenerated = subtitleFile.includes('.auto.');
        return formatTranscriptData(transcriptItems, videoId, lang, isAutoGenerated);
      }
    } else if (subtitleFile.endsWith('.srt')) {
      const transcriptItems = parseSrtSubtitles(subtitleContent);

      // Check if we actually got transcript items
      if (!transcriptItems || transcriptItems.length === 0) {
        console.log(`No transcript items parsed from ${subtitleFile}`);
        return null;
      }

      // Determine if these are auto-generated subtitles
      const isAutoGenerated = subtitleFile.includes('.auto.');
      console.log(`Subtitle file is ${isAutoGenerated ? 'auto-generated' : 'manual'}`);

      // Format the transcript data
      return formatTranscriptData(transcriptItems, videoId, lang, isAutoGenerated);
    } else {
      throw new Error(`Unsupported subtitle format: ${subtitleFile}`);
    }
  } catch (error) {
    console.error(`Error fetching transcript with yt-dlp: ${error.message}`);
    console.error(`Full error: ${error.stack || JSON.stringify(error)}`);
    return null;
  }
}

/**
 * Fetch transcript using web scraping
 * @param {string} videoId - YouTube video ID
 * @returns {Promise<object|null>} Transcript data or null if not found
 */
async function fetchWithWebScraping(videoId) {
  try {
    console.log(`Fetching transcript for ${videoId} using web scraping...`);

    // Fetch the video page
    const response = await axios.get(`https://www.youtube.com/watch?v=${videoId}`);
    const html = response.data;

    // Load the HTML into cheerio
    const $ = cheerio.load(html);

    // Extract the transcript data from the page
    // This is a simplified approach and may not work for all videos
    const scriptTags = $('script').toArray();
    let transcriptData = null;

    for (const scriptTag of scriptTags) {
      const scriptContent = $(scriptTag).html();

      if (scriptContent && scriptContent.includes('"captionTracks"')) {
        // Extract the caption tracks data
        const match = scriptContent.match(/"captionTracks":\s*(\[.*?\])/);

        if (match && match[1]) {
          try {
            // Parse the JSON data
            const captionTracks = JSON.parse(match[1].replace(/\\"/g, '"'));

            if (captionTracks && captionTracks.length > 0) {
              // Get the first caption track
              const captionTrack = captionTracks[0];

              // Get the base URL for the transcript
              const baseUrl = captionTrack.baseUrl;

              if (baseUrl) {
                // Fetch the transcript data
                const transcriptResponse = await axios.get(baseUrl);
                const transcriptXml = transcriptResponse.data;

                // Parse the XML data
                const $ = cheerio.load(transcriptXml, { xmlMode: true });

                // Extract the transcript items
                const transcriptItems = [];

                $('text').each((_, element) => {
                  const start = parseFloat($(element).attr('start'));
                  const duration = parseFloat($(element).attr('dur') || '0');
                  const text = $(element).text();

                  transcriptItems.push({
                    start,
                    end: start + duration,
                    text
                  });
                });

                if (transcriptItems.length > 0) {
                  transcriptData = transcriptItems;
                  break;
                }
              }
            }
          } catch (error) {
            console.error(`Error parsing caption tracks: ${error.message}`);
          }
        }
      }
    }

    if (!transcriptData) {
      console.log('No transcript found with web scraping');
      return null;
    }

    console.log(`Found ${transcriptData.length} transcript items with web scraping`);

    // Web scraping always returns auto-generated subtitles
    const autoGenerated = true;

    // Format the transcript data
    return formatTranscriptData(transcriptData, videoId, 'en', autoGenerated);
  } catch (error) {
    console.error(`Error fetching transcript with web scraping: ${error.message}`);
    return null;
  }
}

// Import cached transcripts for example videos
const cachedTranscripts = require('../data/cached-transcripts');

/**
 * Main function to fetch transcript with fallback methods
 * @param {string} videoId - YouTube video ID
 * @param {string} lang - Language code
 * @param {boolean} forceFresh - Whether to skip cache and fetch fresh data
 * @returns {Promise<object>} Transcript data
 * @throws {Error} If all methods fail
 */
async function getTranscript(videoId, lang = 'en', forceFresh = false) {
  // Allow any language to be requested for any video (including example videos)

  // Step 0: Check if this is an example video with a pre-cached transcript
  if (!forceFresh && videoId in cachedTranscripts) {
    console.log(`Using pre-cached transcript for example video ${videoId}`);
    const transcript = cachedTranscripts[videoId];

    // Check if the cached transcript matches the requested language
    if (transcript.language === lang) {
      console.log(`Using cached transcript for ${videoId} in language ${lang}`);
      return {
        ...transcript,
        isAutoGenerated: transcript.isAutoGenerated || false,
        originalLanguage: transcript.originalLanguage || 'en'
      };
    } else {
      console.log(`Cached transcript for ${videoId} is in language ${transcript.language}, but ${lang} was requested. Fetching ${lang} version.`);
      // Continue to fetch the requested language
    }
  }

  // Step 1: Check cache first (unless forceFresh is true)
  if (!forceFresh) {
    const cachedTranscript = await getFromCache(videoId);
    if (cachedTranscript) {
      // If the cached transcript is in the requested language, use it
      if (cachedTranscript.language === lang) {
        console.log(`Using cached transcript for ${videoId} in language ${lang}`);

        // Add original language information if it's missing
        if (!cachedTranscript.originalLanguage && knownEnglishVideos.includes(videoId)) {
          cachedTranscript.originalLanguage = 'en';
        }

        return cachedTranscript;
      } else {
        console.log(`Cached transcript for ${videoId} is in language ${cachedTranscript.language}, but ${lang} was requested`);

        // Continue to fetch the requested language
      }
    }
  } else {
    console.log(`Skipping cache for ${videoId} due to forceFresh flag`);
  }

  // Step 2: Try each method in sequence
  console.log(`Fetching transcript for ${videoId} with language: ${lang}`);

  // Always prioritize yt-dlp as it's the most reliable method
  // Method 1: yt-dlp (most reliable)
  try {
    console.log(`Trying yt-dlp method first for ${videoId} in language ${lang}...`);
    let transcript = await fetchWithYtDlp(videoId, lang);
    if (transcript) {
      console.log(`Successfully fetched transcript with yt-dlp for ${videoId} in language ${lang}`);
      await saveToCache(videoId, transcript);
      return transcript;
    }
  } catch (error) {
    console.error(`Error with yt-dlp method: ${error.message}`);
  }

  // Method 2: youtube-transcript package
  try {
    console.log(`Trying youtube-transcript method for ${videoId} in language ${lang}...`);
    let transcript = await fetchWithYoutubeTranscript(videoId, lang);
    if (transcript) {
      console.log(`Successfully fetched transcript with youtube-transcript for ${videoId} in language ${lang}`);
      await saveToCache(videoId, transcript);
      return transcript;
    }
  } catch (error) {
    console.error(`Error with youtube-transcript method: ${error.message}`);
  }

  // Method 3: Web scraping
  try {
    console.log(`Trying web scraping method for ${videoId}...`);
    let transcript = await fetchWithWebScraping(videoId);
    if (transcript) {
      console.log(`Successfully fetched transcript with web scraping for ${videoId}`);
      // If the requested language is not English, override the language to match what was requested
      if (lang !== 'en' && lang !== 'en-US' && lang !== 'en-orig') {
        transcript.language = lang;
        transcript.fallbackLanguage = 'en'; // Mark that this is actually English
      }
      await saveToCache(videoId, transcript);
      return transcript;
    }
  } catch (error) {
    console.error(`Error with web scraping method: ${error.message}`);
  }

  // If all methods fail, try one more time with English as a fallback
  if (lang !== 'en' && lang !== 'en-US' && lang !== 'en-orig') {
    console.log(`All methods failed for language ${lang}, trying English as fallback...`);
    try {
      // Try with English
      let transcript = await fetchWithYoutubeTranscript(videoId, 'en');
      if (transcript) {
        console.log(`Successfully fetched English transcript as fallback for ${videoId}`);
        // Override the language to match what was requested for UI consistency
        transcript.language = lang;
        transcript.fallbackLanguage = 'en'; // Mark that this is actually English
        await saveToCache(videoId, transcript);
        return transcript;
      }
    } catch (fallbackError) {
      console.error(`Fallback to English also failed: ${fallbackError.message}`);
    }
  }

  // If we have a cached example video, use it as a last resort
  if (videoId in cachedTranscripts) {
    console.log(`Using cached example video as last resort for ${videoId}`);
    const transcript = cachedTranscripts[videoId];
    return {
      ...transcript,
      language: 'en', // Always return English for cached example videos
      originalLanguage: 'en',
      isAutoGenerated: false,
      isLastResortFallback: true,
      requestedLanguage: lang // Keep track of what was originally requested
    };
  }

  // If all methods fail, return an empty transcript with a detailed error message
  // This is better than throwing an error which causes a 500 response
  console.error(`All transcript fetching methods failed for video ${videoId} in language ${lang}`);

  // Check if the video exists by making a simple request to the YouTube API
  try {
    const videoUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;
    await axios.get(videoUrl);
    // If we get here, the video exists
  } catch (error) {
    // Video doesn't exist or is private
    console.error(`Error checking video existence: ${error.message}`);
    return {
      videoId,
      language: lang,
      transcript: [
        {
          id: 1,
          start: 0,
          end: 10,
          formattedStart: '00:00',
          formattedEnd: '00:10',
          text: 'This video may be private, unavailable, or does not exist.'
        }
      ],
      error: `The video with ID ${videoId} may be private, unavailable, or does not exist.`,
      isError: true
    };
  }

  // Video exists but no transcript found
  return {
    videoId,
    language: lang,
    transcript: [
      {
        id: 1,
        start: 0,
        end: 10,
        formattedStart: '00:00',
        formattedEnd: '00:10',
        text: 'Sorry, no transcript could be found for this video. The video may not have subtitles available.'
      }
    ],
    error: `Failed to fetch transcript for video ${videoId} using all available methods. The video may not have subtitles available.`,
    isError: true
  };
}

module.exports = {
  getTranscript,
  formatTime
};