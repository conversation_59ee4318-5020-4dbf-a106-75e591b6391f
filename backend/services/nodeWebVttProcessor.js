/**
 * Node WebVTT Processor
 *
 * Uses the node-webvtt package to properly parse VTT files
 * and handle different languages correctly.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process a VTT file content using node-webvtt
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent, lang) {
  console.log(`Processing VTT content for language: ${lang} using node-webvtt`);

  try {
    // Parse the VTT content using node-webvtt
    // Set strict to false to handle potential parsing errors
    const parsed = webvtt.parse(vttContent, { strict: false, meta: true });

    if (!parsed || !parsed.cues || parsed.cues.length === 0) {
      console.error('No cues found in VTT content');
      return [];
    }

    console.log(`Parsed ${parsed.cues.length} cues from VTT file`);

    // Convert the parsed cues to our transcript format
    // Special handling for YouTube VTT format with word-level timing
    const transcriptItems = processYouTubeVttCues(parsed.cues);

    // Check if the transcript contains Arabic text when we didn't request Arabic
    // But only for specific languages that might be confused with Arabic
    if (lang !== 'ar' && lang !== 'de' && lang !== 'ru' && lang !== 'ja' && lang !== 'zh') {
      const containsArabic = transcriptItems.some(item =>
        item.text && /[\u0600-\u06FF]/.test(item.text)
      );

      if (containsArabic) {
        console.log('Detected Arabic transcript but requested language is not Arabic. Skipping this method.');
        return [];
      }
    }

    // Clean up the transcript items
    const cleanedItems = cleanupTranscriptItems(transcriptItems);
    console.log(`Cleaned ${cleanedItems.length} transcript items`);

    return cleanedItems;
  } catch (error) {
    console.error(`Error processing VTT content: ${error.message}`);
    // Return an empty array in case of error
    return [];
  }
}

/**
 * Process YouTube VTT cues that contain word-level timing information
 * @param {Array} cues - Parsed VTT cues
 * @returns {Array} Processed transcript items
 */
function processYouTubeVttCues(cues) {
  console.log(`Processing ${cues.length} VTT cues`);

  // YouTube VTT files have a specific pattern:
  // 1. Cues with word-level timing tags (e.g., "thank<00:00:00.399><c> you</c>")
  // 2. Cues with clean text (e.g., "thank you everybody it's great to see")
  // 3. Empty or whitespace-only cues

  // First pass: Extract only the clean text cues (without timing tags)
  const cleanTextCues = [];

  for (const cue of cues) {
    let text = decodeHtmlEntities(cue.text);

    // Skip cues that contain word-level timing tags
    if (text.includes('<') && text.includes('>')) {
      continue;
    }

    // Clean up whitespace and normalize
    text = text.replace(/\s+/g, ' ').trim();

    // Skip empty, whitespace-only, or very short text
    if (!text || text.length < 3) continue;

    // Skip cues that are just punctuation or single words
    if (text.length < 10 && !/[.!?]/.test(text)) continue;

    cleanTextCues.push({
      start: cue.start,
      end: cue.end,
      text: text
    });
  }

  console.log(`After filtering for clean text: ${cleanTextCues.length} cues`);

  // Sort by start time
  cleanTextCues.sort((a, b) => a.start - b.start);

  // Second pass: Merge overlapping and duplicate segments
  const mergedItems = [];

  for (const cue of cleanTextCues) {
    let merged = false;

    // Look for recent items to merge with
    for (let i = mergedItems.length - 1; i >= Math.max(0, mergedItems.length - 3); i--) {
      const existingItem = mergedItems[i];

      // Skip if too far apart in time
      if (cue.start - existingItem.end > 1.0) continue;

      // Case 1: Exact duplicate text
      if (existingItem.text === cue.text) {
        // Extend timing if needed
        existingItem.end = Math.max(existingItem.end, cue.end);
        merged = true;
        break;
      }

      // Case 2: Current text is completely contained in existing text
      if (existingItem.text.includes(cue.text) && cue.text.length > 5) {
        // Extend timing if needed
        existingItem.end = Math.max(existingItem.end, cue.end);
        merged = true;
        break;
      }

      // Case 3: Existing text is completely contained in current text
      if (cue.text.includes(existingItem.text) && existingItem.text.length > 5) {
        // Replace with the longer, more complete text
        existingItem.text = cue.text;
        existingItem.start = Math.min(existingItem.start, cue.start);
        existingItem.end = Math.max(existingItem.end, cue.end);
        merged = true;
        break;
      }

      // Case 4: Sequential text that should be combined
      // Check if this text continues from where the previous text left off
      if (isSequentialText(existingItem.text, cue.text)) {
        const combinedText = combineSequentialText(existingItem.text, cue.text);
        if (combinedText && combinedText !== existingItem.text) {
          existingItem.text = combinedText;
          existingItem.end = cue.end;
          merged = true;
          break;
        }
      }

      // Case 5: Merge short segments that are close in time and form complete sentences
      if (cue.start - existingItem.end <= 0.5 &&
          (existingItem.text.length < 50 || cue.text.length < 50) &&
          !existingItem.text.endsWith('.') && !existingItem.text.endsWith('!') && !existingItem.text.endsWith('?')) {
        // Check if combining them would make a more complete sentence
        const combined = existingItem.text + ' ' + cue.text;
        if (combined.length < 200) { // Don't create overly long segments
          existingItem.text = combined;
          existingItem.end = cue.end;
          merged = true;
          break;
        }
      }
    }

    // If not merged, add as new item
    if (!merged) {
      mergedItems.push({
        start: cue.start,
        end: cue.end,
        text: cue.text
      });
    }
  }

  console.log(`After merging: ${mergedItems.length} transcript items`);

  // Third pass: Clean up any remaining issues
  const finalItems = [];

  for (let i = 0; i < mergedItems.length; i++) {
    const item = mergedItems[i];

    // Skip very short or empty items
    if (!item.text || item.text.length < 5) continue;

    // Check for duplicates with previous items
    let isDuplicate = false;
    for (let j = Math.max(0, finalItems.length - 3); j < finalItems.length; j++) {
      if (finalItems[j].text === item.text) {
        isDuplicate = true;
        break;
      }
    }

    if (!isDuplicate) {
      finalItems.push({
        id: finalItems.length + 1,
        start: item.start,
        end: item.end,
        formattedStart: formatTime(item.start),
        formattedEnd: formatTime(item.end),
        text: item.text
      });
    }
  }

  console.log(`Final result: ${finalItems.length} transcript items`);
  return finalItems;
}

/**
 * Check if two text segments are sequential (one continues from the other)
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {boolean} True if text2 continues from text1
 */
function isSequentialText(text1, text2) {
  if (!text1 || !text2) return false;

  // Normalize texts
  const normalize = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm1 = normalize(text1);
  const norm2 = normalize(text2);

  const words1 = norm1.split(' ');
  const words2 = norm2.split(' ');

  // Check if text2 starts with the last few words of text1
  const lastWords1 = words1.slice(-4); // Check last 4 words
  const firstWords2 = words2.slice(0, 4); // Check first 4 words

  // Look for overlap
  for (let i = 1; i <= Math.min(lastWords1.length, firstWords2.length); i++) {
    const overlap1 = lastWords1.slice(-i);
    const overlap2 = firstWords2.slice(0, i);

    if (overlap1.join(' ') === overlap2.join(' ')) {
      return true;
    }
  }

  return false;
}

/**
 * Combine sequential text segments by removing overlap
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Combined text or null if not suitable
 */
function combineSequentialText(text1, text2) {
  if (!text1 || !text2) return null;

  // Normalize for processing but keep original case
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  const norm1 = text1.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm2 = text2.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();

  const normWords1 = norm1.split(' ');
  const normWords2 = norm2.split(' ');

  // Find the overlap
  let overlapLength = 0;
  for (let i = 1; i <= Math.min(normWords1.length, normWords2.length); i++) {
    const lastNormWords1 = normWords1.slice(-i);
    const firstNormWords2 = normWords2.slice(0, i);

    if (lastNormWords1.join(' ') === firstNormWords2.join(' ')) {
      overlapLength = i;
    }
  }

  if (overlapLength > 0) {
    // Combine by removing the overlapping part from text2
    const remainingWords2 = words2.slice(overlapLength);
    if (remainingWords2.length > 0) {
      return text1 + ' ' + remainingWords2.join(' ');
    } else {
      // text2 is completely contained in text1
      return text1;
    }
  }

  // No overlap found, don't combine
  return null;
}

/**
 * Calculate text similarity between two strings
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {number} Similarity score between 0 and 1
 */
function calculateTextSimilarity(text1, text2) {
  if (!text1 || !text2) return 0;

  // Normalize texts for comparison
  const normalize = (text) => text.toLowerCase().replace(/[^\w\s]/g, '').replace(/\s+/g, ' ').trim();
  const norm1 = normalize(text1);
  const norm2 = normalize(text2);

  if (norm1 === norm2) return 1;

  // Calculate word-based similarity
  const words1 = norm1.split(' ');
  const words2 = norm2.split(' ');

  const allWords = new Set([...words1, ...words2]);
  const commonWords = words1.filter(word => words2.includes(word));

  return commonWords.length / allWords.size;
}

/**
 * Merge complementary text segments
 * @param {string} text1 - First text
 * @param {string} text2 - Second text
 * @returns {string} Merged text or null if not suitable for merging
 */
function mergeComplementaryText(text1, text2) {
  if (!text1 || !text2) return null;

  // Don't merge if one text is much longer than the other (likely not complementary)
  const ratio = Math.max(text1.length, text2.length) / Math.min(text1.length, text2.length);
  if (ratio > 3) return null;

  // Simple approach: if text2 seems to continue text1, append it
  // This is a basic heuristic and could be improved
  const words1 = text1.split(' ');
  const words2 = text2.split(' ');

  // Check if text2 starts with some words from the end of text1
  const lastWords1 = words1.slice(-3);
  const firstWords2 = words2.slice(0, 3);

  let overlap = 0;
  for (let i = 0; i < Math.min(lastWords1.length, firstWords2.length); i++) {
    if (lastWords1[i] === firstWords2[i]) {
      overlap++;
    } else {
      break;
    }
  }

  if (overlap > 0) {
    // Remove the overlapping words from text2 and append
    const remainingWords2 = words2.slice(overlap);
    if (remainingWords2.length > 0) {
      return text1 + ' ' + remainingWords2.join(' ');
    }
  }

  // If no clear overlap pattern, don't merge
  return null;
}

/**
 * Decode HTML entities in text
 * @param {string} text - Text with HTML entities
 * @returns {string} Decoded text
 */
function decodeHtmlEntities(text) {
  if (!text) return '';

  // First use the 'he' library for standard HTML entities
  let decodedText = he.decode(text);

  // Handle common YouTube-specific entities and other escaped characters
  // that might not be properly decoded by the he library
  decodedText = decodedText
    // Common HTML entities
    .replace(/&#39;/g, "'")
    .replace(/&amp;#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&amp;quot;/g, '"')
    .replace(/&amp;amp;/g, '&')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;lt;/g, '<')
    .replace(/&amp;gt;/g, '>')
    // Unicode characters
    .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Other common escaped characters
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\r/g, '\r')
    .replace(/\\\\/g, '\\')
    .replace(/\\'/g, "'")
    .replace(/\\"/g, '"');

  return decodedText;
}

/**
 * Clean up transcript items by fixing punctuation, removing duplicates, etc.
 * @param {Array} items - Transcript items to clean up
 * @returns {Array} Cleaned transcript items
 */
function cleanupTranscriptItems(items) {
  if (!items || items.length === 0) return [];

  const cleanedItems = [];

  // Process each item
  for (let i = 0; i < items.length; i++) {
    if (!items[i].text || items[i].text.trim() === '') continue;

    const cleanedItem = { ...items[i] };

    // Remove any remaining HTML-like tags
    cleanedItem.text = cleanedItem.text.replace(/<[^>]*>/g, '');

    // Ensure spaces after punctuation
    cleanedItem.text = cleanedItem.text.replace(/([.!?,;:])([^\s])/g, '$1 $2');

    // Fix spacing around periods in numbers (e.g., "200 .000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\s+\.(\d+)/g, '$1.$2');

    // Fix incorrect spacing in numbers (e.g., "200. 000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\.(\s+)(\d+)/g, '$1.$3');

    // Remove repeated words (e.g., "I was was going" -> "I was going")
    cleanedItem.text = cleanedItem.text.replace(/\b(\w+)(\s+\1\b)+/gi, '$1');

    // Normalize whitespace
    cleanedItem.text = cleanedItem.text.replace(/\s+/g, ' ').trim();

    // Skip if the text is empty after cleaning
    if (!cleanedItem.text) continue;

    // Skip very short fragments
    if (cleanedItem.text.length < 2) continue;

    // Enhanced duplicate detection
    let isDuplicate = false;

    // Check against recent items (not just the previous one)
    for (let j = Math.max(0, cleanedItems.length - 5); j < cleanedItems.length; j++) {
      const existingItem = cleanedItems[j];

      // Check for exact duplicates
      if (cleanedItem.text === existingItem.text) {
        console.log(`Skipping exact duplicate: "${cleanedItem.text}"`);
        isDuplicate = true;
        break;
      }

      // Check if this text is completely contained in an existing item
      if (existingItem.text.includes(cleanedItem.text) && cleanedItem.text.length > 10) {
        console.log(`Skipping contained text: "${cleanedItem.text}" (contained in "${existingItem.text}")`);
        isDuplicate = true;
        break;
      }

      // Check if an existing item is completely contained in this text
      if (cleanedItem.text.includes(existingItem.text) && existingItem.text.length > 10) {
        console.log(`Replacing shorter text: "${existingItem.text}" with "${cleanedItem.text}"`);
        // Replace the existing item with the longer one
        existingItem.text = cleanedItem.text;
        existingItem.end = cleanedItem.end;
        isDuplicate = true;
        break;
      }
    }

    if (!isDuplicate) {
      cleanedItems.push(cleanedItem);
    }
  }

  // Renumber the IDs
  for (let i = 0; i < cleanedItems.length; i++) {
    cleanedItems[i].id = i + 1;
  }

  // Ensure proper capitalization of sentences
  capitalizeFirstLetterOfSentences(cleanedItems);

  return cleanedItems;
}



/**
 * Capitalize the first letter of each sentence across all transcript items
 * @param {Array} items - Transcript items to process
 */
function capitalizeFirstLetterOfSentences(items) {
  if (!items || items.length === 0) return;

  let shouldCapitalize = true;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (!item.text) continue;

    // Split the text into sentences
    const sentences = item.text.split(/([.!?]\s+)/).filter(Boolean);
    let result = '';

    for (let j = 0; j < sentences.length; j++) {
      const part = sentences[j];

      // If this is just a punctuation mark with space
      if (/^[.!?]\s+$/.test(part)) {
        result += part;
        shouldCapitalize = true;
        continue;
      }

      // If we should capitalize this part
      if (shouldCapitalize) {
        // Find the first letter in the sentence
        const firstLetterMatch = part.match(/[a-zA-Z]/);
        if (firstLetterMatch) {
          const firstLetterIndex = firstLetterMatch.index;
          result += part.substring(0, firstLetterIndex) +
                   part.charAt(firstLetterIndex).toUpperCase() +
                   part.substring(firstLetterIndex + 1);
        } else {
          result += part;
        }
        shouldCapitalize = false;
      } else {
        result += part;
      }
    }

    // Update the item text
    items[i].text = result;

    // Check if this item ends with a sentence-ending punctuation
    if (/[.!?]$/.test(item.text)) {
      shouldCapitalize = true;
    }
  }
}

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  cleanupTranscriptItems,
  formatTime
};
