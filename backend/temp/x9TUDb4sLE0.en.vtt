WEBVTT
Kind: captions
Language: en

00:00:00.080 --> 00:00:02.389 align:start position:0%
 
After<00:00:00.400><c> six</c><00:00:00.640><c> failed</c><00:00:00.880><c> SAS</c><00:00:01.199><c> ideas,</c><00:00:01.680><c> my</c><00:00:01.920><c> seventh</c>

00:00:02.389 --> 00:00:02.399 align:start position:0%
After six failed SAS ideas, my seventh
 

00:00:02.399 --> 00:00:05.349 align:start position:0%
After six failed SAS ideas, my seventh
made<00:00:02.720><c> millions.</c><00:00:03.600><c> This</c><00:00:03.919><c> is</c><00:00:04.160><c> Jacob.</c><00:00:04.799><c> He</c><00:00:05.040><c> built</c><00:00:05.200><c> a</c>

00:00:05.349 --> 00:00:05.359 align:start position:0%
made millions. This is <PERSON>. He built a
 

00:00:05.359 --> 00:00:08.150 align:start position:0%
made millions. This is <PERSON>. He built a
million-dollar<00:00:05.839><c> SAS</c><00:00:06.319><c> 100%</c><00:00:07.040><c> with</c><00:00:07.359><c> no</c><00:00:07.600><c> code.</c>

00:00:08.150 --> 00:00:08.160 align:start position:0%
million-dollar SAS 100% with no code.
 

00:00:08.160 --> 00:00:09.750 align:start position:0%
million-dollar SAS 100% with no code.
So,<00:00:08.400><c> you</c><00:00:08.559><c> don't</c><00:00:08.720><c> need</c><00:00:08.800><c> to</c><00:00:09.040><c> know</c><00:00:09.280><c> how</c><00:00:09.519><c> to</c>

00:00:09.750 --> 00:00:09.760 align:start position:0%
So, you don't need to know how to
 

00:00:09.760 --> 00:00:11.110 align:start position:0%
So, you don't need to know how to
actually<00:00:10.080><c> code.</c><00:00:10.480><c> You</c><00:00:10.639><c> don't</c><00:00:10.719><c> need</c><00:00:10.800><c> to</c><00:00:10.880><c> know</c>

00:00:11.110 --> 00:00:11.120 align:start position:0%
actually code. You don't need to know
 

00:00:11.120 --> 00:00:13.430 align:start position:0%
actually code. You don't need to know
code<00:00:11.440><c> syntax.</c><00:00:12.080><c> The</c><00:00:12.320><c> tool</c><00:00:12.559><c> he</c><00:00:12.800><c> built</c><00:00:12.960><c> it</c><00:00:13.120><c> with</c>

00:00:13.430 --> 00:00:13.440 align:start position:0%
code syntax. The tool he built it with
 

00:00:13.440 --> 00:00:16.550 align:start position:0%
code syntax. The tool he built it with
and<00:00:13.679><c> grew</c><00:00:13.920><c> it</c><00:00:14.080><c> from</c><00:00:14.240><c> 0</c><00:00:14.480><c> to</c><00:00:14.719><c> $1</c><00:00:14.960><c> million</c><00:00:15.599><c> ARR.</c>

00:00:16.550 --> 00:00:16.560 align:start position:0%
and grew it from 0 to $1 million ARR.
 

00:00:16.560 --> 00:00:20.550 align:start position:0%
and grew it from 0 to $1 million ARR.
It's<00:00:16.800><c> not</c><00:00:17.039><c> lovable.</c><00:00:17.760><c> It's</c><00:00:18.000><c> not</c><00:00:18.160><c> cursor.</c><00:00:18.960><c> It's</c>

00:00:20.550 --> 00:00:20.560 align:start position:0%
It's not lovable. It's not cursor. It's
 

00:00:20.560 --> 00:00:22.390 align:start position:0%
It's not lovable. It's not cursor. It's
that's<00:00:20.800><c> the</c><00:00:20.960><c> best</c><00:00:21.119><c> part</c><00:00:21.279><c> of</c><00:00:21.439><c> it</c><00:00:21.600><c> is</c><00:00:21.920><c> anyone</c><00:00:22.160><c> can</c>

00:00:22.390 --> 00:00:22.400 align:start position:0%
that's the best part of it is anyone can
 

00:00:22.400 --> 00:00:23.990 align:start position:0%
that's the best part of it is anyone can
get<00:00:22.560><c> started</c><00:00:22.880><c> and</c><00:00:23.119><c> understand</c><00:00:23.519><c> the</c><00:00:23.760><c> basic</c>

00:00:23.990 --> 00:00:24.000 align:start position:0%
get started and understand the basic
 

00:00:24.000 --> 00:00:26.630 align:start position:0%
get started and understand the basic
frameworks<00:00:24.560><c> even</c><00:00:24.800><c> of</c><00:00:25.279><c> programming.</c><00:00:26.000><c> Jacob</c><00:00:26.400><c> is</c>

00:00:26.630 --> 00:00:26.640 align:start position:0%
frameworks even of programming. Jacob is
 

00:00:26.640 --> 00:00:28.390 align:start position:0%
frameworks even of programming. Jacob is
proof<00:00:26.960><c> that</c><00:00:27.199><c> you</c><00:00:27.439><c> don't</c><00:00:27.599><c> need</c><00:00:27.760><c> any</c><00:00:28.000><c> coding</c>

00:00:28.390 --> 00:00:28.400 align:start position:0%
proof that you don't need any coding
 

00:00:28.400 --> 00:00:30.790 align:start position:0%
proof that you don't need any coding
experience<00:00:28.800><c> to</c><00:00:29.039><c> build</c><00:00:29.279><c> a</c><00:00:29.519><c> profitable</c><00:00:30.000><c> SAS.</c>

00:00:30.790 --> 00:00:30.800 align:start position:0%
experience to build a profitable SAS.
 

00:00:30.800 --> 00:00:32.870 align:start position:0%
experience to build a profitable SAS.
And<00:00:31.359><c> not</c><00:00:31.599><c> knowing</c><00:00:31.840><c> how</c><00:00:32.000><c> to</c><00:00:32.160><c> code</c><00:00:32.559><c> might</c>

00:00:32.870 --> 00:00:32.880 align:start position:0%
And not knowing how to code might
 

00:00:32.880 --> 00:00:34.870 align:start position:0%
And not knowing how to code might
actually<00:00:33.200><c> be</c><00:00:33.440><c> a</c><00:00:33.680><c> huge</c><00:00:33.920><c> advantage.</c><00:00:34.480><c> In</c><00:00:34.719><c> this</c>

00:00:34.870 --> 00:00:34.880 align:start position:0%
actually be a huge advantage. In this
 

00:00:34.880 --> 00:00:36.790 align:start position:0%
actually be a huge advantage. In this
video,<00:00:35.280><c> Jacob</c><00:00:35.680><c> and</c><00:00:35.840><c> Alex</c><00:00:36.160><c> come</c><00:00:36.320><c> on</c><00:00:36.480><c> to</c><00:00:36.559><c> the</c>

00:00:36.790 --> 00:00:36.800 align:start position:0%
video, Jacob and Alex come on to the
 

00:00:36.800 --> 00:00:38.470 align:start position:0%
video, Jacob and Alex come on to the
channel<00:00:36.960><c> to</c><00:00:37.200><c> break</c><00:00:37.360><c> down</c><00:00:37.680><c> exactly</c><00:00:38.160><c> how</c><00:00:38.320><c> they</c>

00:00:38.470 --> 00:00:38.480 align:start position:0%
channel to break down exactly how they
 

00:00:38.480 --> 00:00:41.270 align:start position:0%
channel to break down exactly how they
bootstrapped<00:00:39.040><c> their</c><00:00:39.280><c> SAS</c><00:00:39.680><c> to</c><00:00:40.000><c> $1</c><00:00:40.239><c> million</c><00:00:40.960><c> in</c>

00:00:41.270 --> 00:00:41.280 align:start position:0%
bootstrapped their SAS to $1 million in
 

00:00:41.280 --> 00:00:44.310 align:start position:0%
bootstrapped their SAS to $1 million in
just<00:00:41.760><c> 10</c><00:00:42.000><c> months.</c><00:00:42.879><c> Plus,</c><00:00:43.360><c> we'll</c><00:00:43.680><c> talk</c><00:00:43.840><c> about</c>

00:00:44.310 --> 00:00:44.320 align:start position:0%
just 10 months. Plus, we'll talk about
 

00:00:44.320 --> 00:00:46.549 align:start position:0%
just 10 months. Plus, we'll talk about
the<00:00:44.640><c> secret</c><00:00:44.879><c> to</c><00:00:45.120><c> building</c><00:00:45.440><c> MVPs</c><00:00:46.079><c> with</c><00:00:46.320><c> no</c>

00:00:46.549 --> 00:00:46.559 align:start position:0%
the secret to building MVPs with no
 

00:00:46.559 --> 00:00:48.389 align:start position:0%
the secret to building MVPs with no
code,<00:00:47.200><c> the</c><00:00:47.440><c> marketing</c><00:00:47.760><c> strategy</c><00:00:48.160><c> that</c>

00:00:48.389 --> 00:00:48.399 align:start position:0%
code, the marketing strategy that
 

00:00:48.399 --> 00:00:50.549 align:start position:0%
code, the marketing strategy that
exploded<00:00:48.879><c> the</c><00:00:49.120><c> business,</c><00:00:49.840><c> and</c><00:00:50.079><c> how</c><00:00:50.239><c> you</c><00:00:50.399><c> can</c>

00:00:50.549 --> 00:00:50.559 align:start position:0%
exploded the business, and how you can
 

00:00:50.559 --> 00:00:53.029 align:start position:0%
exploded the business, and how you can
get<00:00:50.719><c> started</c><00:00:51.200><c> building</c><00:00:51.600><c> apps</c><00:00:52.000><c> without</c><00:00:52.399><c> code.</c>

00:00:53.029 --> 00:00:53.039 align:start position:0%
get started building apps without code.
 

00:00:53.039 --> 00:00:56.150 align:start position:0%
get started building apps without code.
Today<00:00:53.520><c> I'm</c><00:00:53.760><c> Pat</c><00:00:54.000><c> Walls</c><00:00:54.640><c> and</c><00:00:54.960><c> this</c><00:00:55.440><c> is</c><00:00:55.760><c> Starter</c>

00:00:56.150 --> 00:00:56.160 align:start position:0%
Today I'm Pat Walls and this is Starter
 

00:00:56.160 --> 00:00:58.869 align:start position:0%
Today I'm Pat Walls and this is Starter
Story.

00:00:58.869 --> 00:00:58.879 align:start position:0%
Story.
 

00:00:58.879 --> 00:01:00.950 align:start position:0%
Story.
All right,<00:00:59.280><c> welcome</c><00:00:59.840><c> Jacob</c><00:01:00.239><c> to</c><00:01:00.480><c> Starter</c>

00:01:00.950 --> 00:01:00.960 align:start position:0%
All right, welcome Jacob to Starter
 

00:01:00.960 --> 00:01:02.630 align:start position:0%
All right, welcome Jacob to Starter
Story.<00:01:01.680><c> Tell</c><00:01:01.920><c> me</c><00:01:02.000><c> a</c><00:01:02.160><c> little</c><00:01:02.320><c> about</c><00:01:02.480><c> the</c>

00:01:02.630 --> 00:01:02.640 align:start position:0%
Story. Tell me a little about the
 

00:01:02.640 --> 00:01:04.390 align:start position:0%
Story. Tell me a little about the
business<00:01:02.879><c> that</c><00:01:03.120><c> you</c><00:01:03.280><c> built</c><00:01:03.600><c> and</c><00:01:03.920><c> what's</c><00:01:04.159><c> your</c>

00:01:04.390 --> 00:01:04.400 align:start position:0%
business that you built and what's your
 

00:01:04.400 --> 00:01:06.469 align:start position:0%
business that you built and what's your
story.<00:01:05.040><c> My</c><00:01:05.280><c> name</c><00:01:05.360><c> is</c><00:01:05.519><c> Jacob</c><00:01:05.840><c> and</c><00:01:06.000><c> I</c><00:01:06.159><c> built</c><00:01:06.320><c> a</c>

00:01:06.469 --> 00:01:06.479 align:start position:0%
story. My name is Jacob and I built a
 

00:01:06.479 --> 00:01:08.149 align:start position:0%
story. My name is Jacob and I built a
million-dollar<00:01:06.960><c> business</c><00:01:07.200><c> with</c><00:01:07.439><c> no</c><00:01:07.680><c> code.</c>

00:01:08.149 --> 00:01:08.159 align:start position:0%
million-dollar business with no code.
 

00:01:08.159 --> 00:01:09.670 align:start position:0%
million-dollar business with no code.
I'm<00:01:08.400><c> a</c><00:01:08.479><c> self-taught</c><00:01:08.960><c> developer</c><00:01:09.360><c> and</c><00:01:09.520><c> the</c>

00:01:09.670 --> 00:01:09.680 align:start position:0%
I'm a self-taught developer and the
 

00:01:09.680 --> 00:01:12.149 align:start position:0%
I'm a self-taught developer and the
co-founder<00:01:10.080><c> of</c><00:01:10.240><c> Faceless</c><00:01:10.799><c> Video.</c><00:01:11.280><c> It's</c><00:01:11.600><c> 100%</c>

00:01:12.149 --> 00:01:12.159 align:start position:0%
co-founder of Faceless Video. It's 100%
 

00:01:12.159 --> 00:01:14.310 align:start position:0%
co-founder of Faceless Video. It's 100%
bootstrapped<00:01:12.960><c> and</c><00:01:13.200><c> I</c><00:01:13.439><c> built</c><00:01:13.600><c> it</c><00:01:13.760><c> from</c><00:01:14.000><c> scratch</c>

00:01:14.310 --> 00:01:14.320 align:start position:0%
bootstrapped and I built it from scratch
 

00:01:14.320 --> 00:01:16.230 align:start position:0%
bootstrapped and I built it from scratch
with<00:01:14.560><c> Bubble</c><00:01:15.119><c> and</c><00:01:15.280><c> it's</c><00:01:15.520><c> now</c><00:01:15.680><c> doing</c><00:01:15.840><c> over</c><00:01:16.080><c> a</c>

00:01:16.230 --> 00:01:16.240 align:start position:0%
with Bubble and it's now doing over a
 

00:01:16.240 --> 00:01:18.390 align:start position:0%
with Bubble and it's now doing over a
million<00:01:16.400><c> dollars</c><00:01:16.560><c> a</c><00:01:16.720><c> year</c><00:01:16.880><c> in</c><00:01:17.119><c> ARR.</c><00:01:18.000><c> Wow,</c>

00:01:18.390 --> 00:01:18.400 align:start position:0%
million dollars a year in ARR. Wow,
 

00:01:18.400 --> 00:01:20.149 align:start position:0%
million dollars a year in ARR. Wow,
that's<00:01:18.640><c> amazing.</c><00:01:19.200><c> Uh,</c><00:01:19.520><c> can</c><00:01:19.680><c> you</c><00:01:19.759><c> give</c><00:01:19.840><c> me</c><00:01:20.000><c> a</c>

00:01:20.149 --> 00:01:20.159 align:start position:0%
that's amazing. Uh, can you give me a
 

00:01:20.159 --> 00:01:22.390 align:start position:0%
that's amazing. Uh, can you give me a
further<00:01:20.400><c> breakdown</c><00:01:20.880><c> of</c><00:01:21.200><c> how</c><00:01:21.520><c> Faceless</c><00:01:22.159><c> works,</c>

00:01:22.390 --> 00:01:22.400 align:start position:0%
further breakdown of how Faceless works,
 

00:01:22.400 --> 00:01:23.670 align:start position:0%
further breakdown of how Faceless works,
what<00:01:22.560><c> it</c><00:01:22.720><c> does,</c><00:01:22.880><c> and</c><00:01:23.119><c> some</c><00:01:23.280><c> of</c><00:01:23.280><c> the</c><00:01:23.439><c> numbers</c>

00:01:23.670 --> 00:01:23.680 align:start position:0%
what it does, and some of the numbers
 

00:01:23.680 --> 00:01:25.350 align:start position:0%
what it does, and some of the numbers
behind<00:01:24.000><c> the</c><00:01:24.159><c> business?</c><00:01:24.560><c> It's</c><00:01:24.720><c> a</c><00:01:24.880><c> SAS</c><00:01:25.200><c> that</c>

00:01:25.350 --> 00:01:25.360 align:start position:0%
behind the business? It's a SAS that
 

00:01:25.360 --> 00:01:27.350 align:start position:0%
behind the business? It's a SAS that
automates<00:01:25.680><c> Faceless</c><00:01:26.159><c> social</c><00:01:26.400><c> media</c><00:01:26.720><c> channels</c>

00:01:27.350 --> 00:01:27.360 align:start position:0%
automates Faceless social media channels
 

00:01:27.360 --> 00:01:29.429 align:start position:0%
automates Faceless social media channels
and<00:01:27.680><c> the</c><00:01:27.920><c> user</c><00:01:28.159><c> only</c><00:01:28.479><c> needs</c><00:01:28.640><c> to</c><00:01:28.799><c> provide</c><00:01:29.200><c> their</c>

00:01:29.429 --> 00:01:29.439 align:start position:0%
and the user only needs to provide their
 

00:01:29.439 --> 00:01:31.670 align:start position:0%
and the user only needs to provide their
channel<00:01:29.759><c> topic.</c><00:01:30.320><c> And</c><00:01:30.560><c> our</c><00:01:30.720><c> platform</c><00:01:31.360><c> writes</c>

00:01:31.670 --> 00:01:31.680 align:start position:0%
channel topic. And our platform writes
 

00:01:31.680 --> 00:01:33.670 align:start position:0%
channel topic. And our platform writes
the<00:01:31.920><c> video</c><00:01:32.159><c> content,</c><00:01:32.720><c> creates</c><00:01:33.040><c> the</c><00:01:33.280><c> video,</c>

00:01:33.670 --> 00:01:33.680 align:start position:0%
the video content, creates the video,
 

00:01:33.680 --> 00:01:35.350 align:start position:0%
the video content, creates the video,
and<00:01:33.920><c> posts</c><00:01:34.240><c> that</c><00:01:34.479><c> video</c><00:01:34.640><c> on</c><00:01:34.880><c> the</c><00:01:35.040><c> user's</c>

00:01:35.350 --> 00:01:35.360 align:start position:0%
and posts that video on the user's
 

00:01:35.360 --> 00:01:37.429 align:start position:0%
and posts that video on the user's
behalf<00:01:35.840><c> every</c><00:01:36.079><c> single</c><00:01:36.320><c> day,</c><00:01:36.880><c> entirely</c><00:01:37.280><c> on</c>

00:01:37.429 --> 00:01:37.439 align:start position:0%
behalf every single day, entirely on
 

00:01:37.439 --> 00:01:39.350 align:start position:0%
behalf every single day, entirely on
autopilot.<00:01:38.240><c> We're</c><00:01:38.479><c> generating</c><00:01:38.880><c> thousands</c><00:01:39.200><c> of</c>

00:01:39.350 --> 00:01:39.360 align:start position:0%
autopilot. We're generating thousands of
 

00:01:39.360 --> 00:01:41.749 align:start position:0%
autopilot. We're generating thousands of
videos<00:01:39.759><c> every</c><00:01:40.079><c> single</c><00:01:40.320><c> day</c><00:01:40.640><c> for</c><00:01:40.880><c> users.</c><00:01:41.600><c> Some</c>

00:01:41.749 --> 00:01:41.759 align:start position:0%
videos every single day for users. Some
 

00:01:41.759 --> 00:01:43.670 align:start position:0%
videos every single day for users. Some
of<00:01:41.920><c> which</c><00:01:42.240><c> had</c><00:01:42.400><c> well</c><00:01:42.640><c> over</c><00:01:42.799><c> a</c><00:01:42.880><c> million</c><00:01:43.200><c> views,</c>

00:01:43.670 --> 00:01:43.680 align:start position:0%
of which had well over a million views,
 

00:01:43.680 --> 00:01:44.950 align:start position:0%
of which had well over a million views,
plenty<00:01:43.920><c> of</c><00:01:44.079><c> which</c><00:01:44.240><c> have</c><00:01:44.400><c> had</c><00:01:44.560><c> hundreds</c><00:01:44.799><c> of</c>

00:01:44.950 --> 00:01:44.960 align:start position:0%
plenty of which have had hundreds of
 

00:01:44.960 --> 00:01:46.469 align:start position:0%
plenty of which have had hundreds of
thousands<00:01:45.280><c> of</c><00:01:45.360><c> views.</c><00:01:45.920><c> Total</c><00:01:46.240><c> number</c><00:01:46.320><c> of</c>

00:01:46.469 --> 00:01:46.479 align:start position:0%
thousands of views. Total number of
 

00:01:46.479 --> 00:01:48.710 align:start position:0%
thousands of views. Total number of
users<00:01:46.799><c> signed</c><00:01:46.960><c> up,</c><00:01:47.200><c> over</c><00:01:47.439><c> 1.1</c><00:01:48.000><c> million.</c><00:01:48.479><c> and</c>

00:01:48.710 --> 00:01:48.720 align:start position:0%
users signed up, over 1.1 million. and
 

00:01:48.720 --> 00:01:50.630 align:start position:0%
users signed up, over 1.1 million. and
they're<00:01:48.960><c> all</c><00:01:49.439><c> hopping</c><00:01:49.759><c> on</c><00:01:50.159><c> current</c><00:01:50.399><c> and</c>

00:01:50.630 --> 00:01:50.640 align:start position:0%
they're all hopping on current and
 

00:01:50.640 --> 00:01:52.069 align:start position:0%
they're all hopping on current and
relevant<00:01:50.880><c> trends</c><00:01:51.200><c> right</c><00:01:51.360><c> now.</c><00:01:51.680><c> That's</c><00:01:51.920><c> an</c>

00:01:52.069 --> 00:01:52.079 align:start position:0%
relevant trends right now. That's an
 

00:01:52.079 --> 00:01:54.149 align:start position:0%
relevant trends right now. That's an
amazing<00:01:52.399><c> business</c><00:01:52.560><c> that</c><00:01:52.799><c> you</c><00:01:52.960><c> built.</c><00:01:53.759><c> But</c><00:01:53.920><c> I</c>

00:01:54.149 --> 00:01:54.159 align:start position:0%
amazing business that you built. But I
 

00:01:54.159 --> 00:01:56.550 align:start position:0%
amazing business that you built. But I
want<00:01:54.240><c> to</c><00:01:54.320><c> go</c><00:01:54.560><c> back</c><00:01:54.880><c> a</c><00:01:55.119><c> little</c><00:01:55.280><c> bit.</c><00:01:56.000><c> Uh</c><00:01:56.320><c> what's</c>

00:01:56.550 --> 00:01:56.560 align:start position:0%
want to go back a little bit. Uh what's
 

00:01:56.560 --> 00:01:58.230 align:start position:0%
want to go back a little bit. Uh what's
your<00:01:56.799><c> background</c><00:01:57.439><c> and</c><00:01:57.680><c> how</c><00:01:57.759><c> do</c><00:01:57.840><c> you</c><00:01:57.920><c> get</c><00:01:58.079><c> to</c>

00:01:58.230 --> 00:01:58.240 align:start position:0%
your background and how do you get to
 

00:01:58.240 --> 00:02:00.310 align:start position:0%
your background and how do you get to
the<00:01:58.320><c> point</c><00:01:58.479><c> of</c><00:01:58.640><c> building</c><00:01:59.119><c> a</c><00:01:59.280><c> SAS?</c><00:02:00.079><c> My</c>

00:02:00.310 --> 00:02:00.320 align:start position:0%
the point of building a SAS? My
 

00:02:00.320 --> 00:02:02.789 align:start position:0%
the point of building a SAS? My
background<00:02:00.880><c> started</c><00:02:01.200><c> as</c><00:02:01.360><c> an</c><00:02:01.520><c> artist.</c><00:02:02.079><c> I</c><00:02:02.560><c> uh</c>

00:02:02.789 --> 00:02:02.799 align:start position:0%
background started as an artist. I uh
 

00:02:02.799 --> 00:02:05.190 align:start position:0%
background started as an artist. I uh
started<00:02:03.040><c> as</c><00:02:03.200><c> a</c><00:02:03.360><c> songwriter,</c><00:02:04.240><c> producer,</c><00:02:04.880><c> and</c><00:02:05.040><c> I</c>

00:02:05.190 --> 00:02:05.200 align:start position:0%
started as a songwriter, producer, and I
 

00:02:05.200 --> 00:02:06.950 align:start position:0%
started as a songwriter, producer, and I
was<00:02:05.360><c> releasing</c><00:02:05.759><c> music</c><00:02:05.920><c> on</c><00:02:06.079><c> Spotify,</c><00:02:06.560><c> diving</c>

00:02:06.950 --> 00:02:06.960 align:start position:0%
was releasing music on Spotify, diving
 

00:02:06.960 --> 00:02:09.350 align:start position:0%
was releasing music on Spotify, diving
full<00:02:07.200><c> force</c><00:02:07.600><c> into</c><00:02:08.080><c> that</c><00:02:08.399><c> world.</c><00:02:08.879><c> So</c><00:02:09.039><c> when</c><00:02:09.200><c> I</c>

00:02:09.350 --> 00:02:09.360 align:start position:0%
full force into that world. So when I
 

00:02:09.360 --> 00:02:11.110 align:start position:0%
full force into that world. So when I
graduated<00:02:09.840><c> college,</c><00:02:10.239><c> I</c><00:02:10.399><c> decided</c><00:02:10.640><c> to</c><00:02:10.800><c> live</c><00:02:10.959><c> at</c>

00:02:11.110 --> 00:02:11.120 align:start position:0%
graduated college, I decided to live at
 

00:02:11.120 --> 00:02:13.270 align:start position:0%
graduated college, I decided to live at
home<00:02:11.280><c> with</c><00:02:11.440><c> my</c><00:02:11.599><c> mom</c><00:02:12.160><c> and</c><00:02:12.400><c> go</c><00:02:12.560><c> full</c><00:02:12.800><c> force</c><00:02:13.040><c> into</c>

00:02:13.270 --> 00:02:13.280 align:start position:0%
home with my mom and go full force into
 

00:02:13.280 --> 00:02:15.670 align:start position:0%
home with my mom and go full force into
being<00:02:13.440><c> a</c><00:02:13.520><c> songwriter,</c><00:02:14.000><c> artist,</c><00:02:14.400><c> producer.</c><00:02:15.040><c> I</c>

00:02:15.670 --> 00:02:15.680 align:start position:0%
being a songwriter, artist, producer. I
 

00:02:15.680 --> 00:02:17.589 align:start position:0%
being a songwriter, artist, producer. I
started<00:02:16.000><c> hiring</c><00:02:16.480><c> companies</c><00:02:16.879><c> to</c><00:02:17.120><c> do</c><00:02:17.280><c> marketing</c>

00:02:17.589 --> 00:02:17.599 align:start position:0%
started hiring companies to do marketing
 

00:02:17.599 --> 00:02:19.670 align:start position:0%
started hiring companies to do marketing
for<00:02:17.840><c> me,</c><00:02:18.239><c> but</c><00:02:18.480><c> I</c><00:02:18.640><c> I</c><00:02:18.879><c> was</c><00:02:19.040><c> just</c><00:02:19.200><c> repeatedly</c>

00:02:19.670 --> 00:02:19.680 align:start position:0%
for me, but I I was just repeatedly
 

00:02:19.680 --> 00:02:21.030 align:start position:0%
for me, but I I was just repeatedly
disappointed<00:02:20.160><c> by</c><00:02:20.480><c> all</c><00:02:20.640><c> the</c><00:02:20.879><c> different</c>

00:02:21.030 --> 00:02:21.040 align:start position:0%
disappointed by all the different
 

00:02:21.040 --> 00:02:22.630 align:start position:0%
disappointed by all the different
marketing<00:02:21.360><c> services</c><00:02:21.680><c> that</c><00:02:21.840><c> I</c><00:02:22.000><c> was</c><00:02:22.160><c> getting.</c>

00:02:22.630 --> 00:02:22.640 align:start position:0%
marketing services that I was getting.
 

00:02:22.640 --> 00:02:24.390 align:start position:0%
marketing services that I was getting.
So,<00:02:22.800><c> I</c><00:02:22.959><c> just</c><00:02:23.120><c> figured,</c><00:02:23.520><c> okay,</c><00:02:23.760><c> I</c><00:02:23.920><c> got</c><00:02:24.080><c> to</c><00:02:24.160><c> go</c><00:02:24.319><c> do</c>

00:02:24.390 --> 00:02:24.400 align:start position:0%
So, I just figured, okay, I got to go do
 

00:02:24.400 --> 00:02:26.470 align:start position:0%
So, I just figured, okay, I got to go do
this<00:02:24.640><c> myself.</c><00:02:25.280><c> So,</c><00:02:25.440><c> I</c><00:02:25.599><c> took</c><00:02:25.840><c> courses</c><00:02:26.160><c> online</c>

00:02:26.470 --> 00:02:26.480 align:start position:0%
this myself. So, I took courses online
 

00:02:26.480 --> 00:02:29.030 align:start position:0%
this myself. So, I took courses online
about<00:02:26.720><c> how</c><00:02:26.879><c> to</c><00:02:26.959><c> market</c><00:02:27.280><c> my</c><00:02:27.520><c> music</c><00:02:28.160><c> and</c><00:02:28.800><c> with</c>

00:02:29.030 --> 00:02:29.040 align:start position:0%
about how to market my music and with
 

00:02:29.040 --> 00:02:30.949 align:start position:0%
about how to market my music and with
very<00:02:29.360><c> little</c><00:02:29.520><c> budget</c><00:02:29.920><c> took</c><00:02:30.160><c> my</c><00:02:30.319><c> music</c><00:02:30.720><c> to</c>

00:02:30.949 --> 00:02:30.959 align:start position:0%
very little budget took my music to
 

00:02:30.959 --> 00:02:33.110 align:start position:0%
very little budget took my music to
close<00:02:31.200><c> to</c><00:02:31.280><c> a</c><00:02:31.520><c> million</c><00:02:31.760><c> streams</c><00:02:32.400><c> and</c><00:02:32.720><c> thousands</c>

00:02:33.110 --> 00:02:33.120 align:start position:0%
close to a million streams and thousands
 

00:02:33.120 --> 00:02:34.790 align:start position:0%
close to a million streams and thousands
of<00:02:33.280><c> genuine</c><00:02:33.680><c> fans.</c><00:02:34.160><c> That</c><00:02:34.400><c> naturally</c>

00:02:34.790 --> 00:02:34.800 align:start position:0%
of genuine fans. That naturally
 

00:02:34.800 --> 00:02:36.309 align:start position:0%
of genuine fans. That naturally
progressed<00:02:35.120><c> into</c><00:02:35.519><c> helping</c><00:02:35.840><c> my</c><00:02:36.000><c> friends</c><00:02:36.160><c> out</c>

00:02:36.309 --> 00:02:36.319 align:start position:0%
progressed into helping my friends out
 

00:02:36.319 --> 00:02:37.990 align:start position:0%
progressed into helping my friends out
in<00:02:36.560><c> the</c><00:02:36.640><c> music</c><00:02:36.879><c> business</c><00:02:37.280><c> and</c><00:02:37.599><c> helping</c><00:02:37.840><c> them</c>

00:02:37.990 --> 00:02:38.000 align:start position:0%
in the music business and helping them
 

00:02:38.000 --> 00:02:39.670 align:start position:0%
in the music business and helping them
promote<00:02:38.319><c> their</c><00:02:38.480><c> music,</c><00:02:38.959><c> which</c><00:02:39.200><c> turned</c><00:02:39.440><c> into</c>

00:02:39.670 --> 00:02:39.680 align:start position:0%
promote their music, which turned into
 

00:02:39.680 --> 00:02:41.430 align:start position:0%
promote their music, which turned into
my<00:02:39.840><c> first</c><00:02:40.000><c> business,</c><00:02:40.319><c> Domino.</c><00:02:40.879><c> It's</c><00:02:41.040><c> a</c><00:02:41.200><c> music</c>

00:02:41.430 --> 00:02:41.440 align:start position:0%
my first business, Domino. It's a music
 

00:02:41.440 --> 00:02:43.270 align:start position:0%
my first business, Domino. It's a music
marketing<00:02:41.760><c> agency.</c><00:02:42.239><c> And</c><00:02:42.400><c> while</c><00:02:42.640><c> I</c><00:02:42.879><c> love</c><00:02:43.040><c> that</c>

00:02:43.270 --> 00:02:43.280 align:start position:0%
marketing agency. And while I love that
 

00:02:43.280 --> 00:02:45.270 align:start position:0%
marketing agency. And while I love that
experience,<00:02:44.080><c> what</c><00:02:44.239><c> I</c><00:02:44.480><c> realized</c><00:02:44.800><c> about</c><00:02:45.040><c> doing</c>

00:02:45.270 --> 00:02:45.280 align:start position:0%
experience, what I realized about doing
 

00:02:45.280 --> 00:02:46.949 align:start position:0%
experience, what I realized about doing
a<00:02:45.440><c> service-based</c><00:02:46.080><c> business</c><00:02:46.319><c> is</c><00:02:46.560><c> that</c><00:02:46.720><c> my</c>

00:02:46.949 --> 00:02:46.959 align:start position:0%
a service-based business is that my
 

00:02:46.959 --> 00:02:49.030 align:start position:0%
a service-based business is that my
income<00:02:47.360><c> was</c><00:02:47.680><c> directly</c><00:02:48.160><c> correlated</c><00:02:48.640><c> with</c><00:02:48.800><c> my</c>

00:02:49.030 --> 00:02:49.040 align:start position:0%
income was directly correlated with my
 

00:02:49.040 --> 00:02:50.949 align:start position:0%
income was directly correlated with my
time.<00:02:49.360><c> And</c><00:02:49.519><c> I</c><00:02:49.760><c> just</c><00:02:49.920><c> wanted</c><00:02:50.080><c> to</c><00:02:50.239><c> scale</c><00:02:50.480><c> beyond</c>

00:02:50.949 --> 00:02:50.959 align:start position:0%
time. And I just wanted to scale beyond
 

00:02:50.959 --> 00:02:53.430 align:start position:0%
time. And I just wanted to scale beyond
my<00:02:51.280><c> agency</c><00:02:51.680><c> and</c><00:02:51.920><c> help</c><00:02:52.160><c> more</c><00:02:52.480><c> people</c><00:02:52.879><c> without</c>

00:02:53.430 --> 00:02:53.440 align:start position:0%
my agency and help more people without
 

00:02:53.440 --> 00:02:54.869 align:start position:0%
my agency and help more people without
sacrificing<00:02:54.000><c> more</c><00:02:54.160><c> of</c><00:02:54.239><c> my</c><00:02:54.400><c> time.</c><00:02:54.560><c> And</c><00:02:54.720><c> that</c>

00:02:54.869 --> 00:02:54.879 align:start position:0%
sacrificing more of my time. And that
 

00:02:54.879 --> 00:02:57.509 align:start position:0%
sacrificing more of my time. And that
made<00:02:55.040><c> me</c><00:02:55.200><c> start</c><00:02:55.360><c> looking</c><00:02:55.760><c> into</c><00:02:56.560><c> SAS.</c><00:02:57.200><c> So,</c><00:02:57.360><c> you</c>

00:02:57.509 --> 00:02:57.519 align:start position:0%
made me start looking into SAS. So, you
 

00:02:57.519 --> 00:02:59.830 align:start position:0%
made me start looking into SAS. So, you
caught<00:02:57.680><c> the</c><00:02:57.840><c> bug</c><00:02:58.160><c> and</c><00:02:58.400><c> then</c><00:02:58.640><c> you</c><00:02:58.879><c> came</c><00:02:59.040><c> upon</c><00:02:59.519><c> an</c>

00:02:59.830 --> 00:02:59.840 align:start position:0%
caught the bug and then you came upon an
 

00:02:59.840 --> 00:03:02.149 align:start position:0%
caught the bug and then you came upon an
idea<00:03:00.319><c> that</c><00:03:00.640><c> kind</c><00:03:00.800><c> of</c><00:03:00.879><c> changed</c><00:03:01.280><c> everything.</c>

00:03:02.149 --> 00:03:02.159 align:start position:0%
idea that kind of changed everything.
 

00:03:02.159 --> 00:03:03.670 align:start position:0%
idea that kind of changed everything.
Let's<00:03:02.400><c> talk</c><00:03:02.560><c> about</c><00:03:02.720><c> that</c><00:03:02.959><c> idea</c><00:03:03.280><c> and</c><00:03:03.519><c> I</c>

00:03:03.670 --> 00:03:03.680 align:start position:0%
Let's talk about that idea and I
 

00:03:03.680 --> 00:03:05.430 align:start position:0%
Let's talk about that idea and I
actually<00:03:04.000><c> had</c><00:03:04.239><c> you</c><00:03:04.400><c> bring</c><00:03:04.640><c> your</c><00:03:04.800><c> co-founder,</c>

00:03:05.430 --> 00:03:05.440 align:start position:0%
actually had you bring your co-founder,
 

00:03:05.440 --> 00:03:07.910 align:start position:0%
actually had you bring your co-founder,
Alex,<00:03:05.760><c> on</c><00:03:06.080><c> who</c><00:03:06.480><c> was</c><00:03:06.879><c> a</c><00:03:07.120><c> part</c><00:03:07.280><c> of</c><00:03:07.519><c> coming</c><00:03:07.760><c> up</c>

00:03:07.910 --> 00:03:07.920 align:start position:0%
Alex, on who was a part of coming up
 

00:03:07.920 --> 00:03:10.070 align:start position:0%
Alex, on who was a part of coming up
with<00:03:08.080><c> that</c><00:03:08.239><c> idea.</c><00:03:08.879><c> So,</c><00:03:09.120><c> Alex</c><00:03:09.440><c> is</c><00:03:09.599><c> joining</c><00:03:09.840><c> us</c>

00:03:10.070 --> 00:03:10.080 align:start position:0%
with that idea. So, Alex is joining us
 

00:03:10.080 --> 00:03:12.070 align:start position:0%
with that idea. So, Alex is joining us
right<00:03:10.239><c> now.</c><00:03:10.640><c> I'd</c><00:03:10.879><c> love</c><00:03:11.040><c> to</c><00:03:11.200><c> hear</c><00:03:11.280><c> from</c><00:03:11.519><c> you.</c>

00:03:12.070 --> 00:03:12.080 align:start position:0%
right now. I'd love to hear from you.
 

00:03:12.080 --> 00:03:13.830 align:start position:0%
right now. I'd love to hear from you.
Uh,<00:03:12.480><c> how</c><00:03:12.640><c> did</c><00:03:12.720><c> you</c><00:03:12.879><c> come</c><00:03:13.040><c> up</c><00:03:13.200><c> with</c><00:03:13.360><c> the</c><00:03:13.519><c> idea</c>

00:03:13.830 --> 00:03:13.840 align:start position:0%
Uh, how did you come up with the idea
 

00:03:13.840 --> 00:03:16.309 align:start position:0%
Uh, how did you come up with the idea
for<00:03:14.159><c> Faceless</c><00:03:14.640><c> Video?</c><00:03:15.280><c> Yeah,</c><00:03:15.599><c> so</c><00:03:15.920><c> thanks</c><00:03:16.159><c> for</c>

00:03:16.309 --> 00:03:16.319 align:start position:0%
for Faceless Video? Yeah, so thanks for
 

00:03:16.319 --> 00:03:18.470 align:start position:0%
for Faceless Video? Yeah, so thanks for
having<00:03:16.560><c> me.</c><00:03:16.879><c> I</c><00:03:17.680><c> was</c><00:03:17.920><c> getting</c><00:03:18.080><c> a</c><00:03:18.319><c> lot</c><00:03:18.400><c> of</c>

00:03:18.470 --> 00:03:18.480 align:start position:0%
having me. I was getting a lot of
 

00:03:18.480 --> 00:03:21.030 align:start position:0%
having me. I was getting a lot of
Faceless<00:03:19.040><c> videos</c><00:03:19.360><c> on</c><00:03:19.599><c> my</c><00:03:19.840><c> Tik</c><00:03:20.000><c> Tok</c><00:03:20.319><c> algorithm.</c>

00:03:21.030 --> 00:03:21.040 align:start position:0%
Faceless videos on my Tik Tok algorithm.
 

00:03:21.040 --> 00:03:23.509 align:start position:0%
Faceless videos on my Tik Tok algorithm.
I<00:03:21.200><c> had</c><00:03:21.440><c> seen</c><00:03:21.840><c> a</c><00:03:22.159><c> lot</c><00:03:22.239><c> of</c><00:03:22.319><c> these</c><00:03:22.640><c> videos</c><00:03:23.120><c> go</c>

00:03:23.509 --> 00:03:23.519 align:start position:0%
I had seen a lot of these videos go
 

00:03:23.519 --> 00:03:26.149 align:start position:0%
I had seen a lot of these videos go
extremely<00:03:24.000><c> viral,</c><00:03:24.560><c> and</c><00:03:24.800><c> my</c><00:03:25.120><c> goal</c><00:03:25.519><c> originally</c>

00:03:26.149 --> 00:03:26.159 align:start position:0%
extremely viral, and my goal originally
 

00:03:26.159 --> 00:03:28.149 align:start position:0%
extremely viral, and my goal originally
was<00:03:26.400><c> just</c><00:03:26.720><c> to</c><00:03:26.879><c> go</c><00:03:27.040><c> viral.</c><00:03:27.519><c> And</c><00:03:27.680><c> I</c><00:03:27.840><c> thought</c><00:03:28.000><c> to</c>

00:03:28.149 --> 00:03:28.159 align:start position:0%
was just to go viral. And I thought to
 

00:03:28.159 --> 00:03:29.910 align:start position:0%
was just to go viral. And I thought to
myself,<00:03:28.640><c> I</c><00:03:28.800><c> can</c><00:03:28.959><c> edit</c><00:03:29.200><c> these</c><00:03:29.440><c> videos.</c><00:03:29.840><c> You</c>

00:03:29.910 --> 00:03:29.920 align:start position:0%
myself, I can edit these videos. You
 

00:03:29.920 --> 00:03:31.190 align:start position:0%
myself, I can edit these videos. You
know,<00:03:30.080><c> they</c><00:03:30.239><c> look</c><00:03:30.400><c> simple</c><00:03:30.640><c> enough.</c><00:03:30.959><c> They're</c>

00:03:31.190 --> 00:03:31.200 align:start position:0%
know, they look simple enough. They're
 

00:03:31.200 --> 00:03:33.430 align:start position:0%
know, they look simple enough. They're
going<00:03:31.360><c> viral.</c><00:03:32.000><c> Why</c><00:03:32.159><c> not</c><00:03:32.319><c> do</c><00:03:32.480><c> it</c><00:03:32.640><c> myself?</c><00:03:33.280><c> But</c>

00:03:33.430 --> 00:03:33.440 align:start position:0%
going viral. Why not do it myself? But
 

00:03:33.440 --> 00:03:35.589 align:start position:0%
going viral. Why not do it myself? But
then<00:03:33.599><c> I</c><00:03:33.760><c> realized</c><00:03:34.080><c> that</c><00:03:34.480><c> consistency</c><00:03:35.200><c> was</c><00:03:35.360><c> a</c>

00:03:35.589 --> 00:03:35.599 align:start position:0%
then I realized that consistency was a
 

00:03:35.599 --> 00:03:37.750 align:start position:0%
then I realized that consistency was a
huge<00:03:35.840><c> problem.</c><00:03:36.480><c> and</c><00:03:36.959><c> coming</c><00:03:37.200><c> up</c><00:03:37.360><c> with</c><00:03:37.519><c> new</c>

00:03:37.750 --> 00:03:37.760 align:start position:0%
huge problem. and coming up with new
 

00:03:37.760 --> 00:03:39.750 align:start position:0%
huge problem. and coming up with new
ideas,<00:03:38.239><c> editing</c><00:03:38.640><c> every</c><00:03:38.959><c> day</c><00:03:39.360><c> became</c>

00:03:39.750 --> 00:03:39.760 align:start position:0%
ideas, editing every day became
 

00:03:39.760 --> 00:03:41.589 align:start position:0%
ideas, editing every day became
something<00:03:40.080><c> that</c><00:03:40.319><c> was</c><00:03:40.879><c> actually</c><00:03:41.280><c> pretty</c>

00:03:41.589 --> 00:03:41.599 align:start position:0%
something that was actually pretty
 

00:03:41.599 --> 00:03:44.390 align:start position:0%
something that was actually pretty
difficult<00:03:41.920><c> to</c><00:03:42.159><c> stick</c><00:03:42.319><c> to.</c><00:03:43.040><c> So,</c><00:03:43.599><c> I</c><00:03:43.760><c> had</c><00:03:43.920><c> an</c><00:03:44.159><c> idea</c>

00:03:44.390 --> 00:03:44.400 align:start position:0%
difficult to stick to. So, I had an idea
 

00:03:44.400 --> 00:03:46.309 align:start position:0%
difficult to stick to. So, I had an idea
because<00:03:44.640><c> I</c><00:03:44.799><c> realized</c><00:03:45.120><c> with,</c><00:03:45.519><c> you</c><00:03:45.680><c> know,</c><00:03:45.920><c> the</c>

00:03:46.309 --> 00:03:46.319 align:start position:0%
because I realized with, you know, the
 

00:03:46.319 --> 00:03:48.070 align:start position:0%
because I realized with, you know, the
advancements<00:03:46.799><c> in</c><00:03:46.959><c> tech</c><00:03:47.200><c> that</c><00:03:47.360><c> were</c><00:03:47.519><c> going</c><00:03:47.680><c> on,</c>

00:03:48.070 --> 00:03:48.080 align:start position:0%
advancements in tech that were going on,
 

00:03:48.080 --> 00:03:49.430 align:start position:0%
advancements in tech that were going on,
these<00:03:48.319><c> videos</c><00:03:48.560><c> were</c><00:03:48.799><c> simple</c><00:03:49.040><c> enough</c><00:03:49.200><c> that</c>

00:03:49.430 --> 00:03:49.440 align:start position:0%
these videos were simple enough that
 

00:03:49.440 --> 00:03:51.190 align:start position:0%
these videos were simple enough that
this<00:03:49.599><c> could</c><00:03:49.760><c> be</c><00:03:49.920><c> done</c><00:03:50.159><c> automatically.</c><00:03:51.040><c> And</c>

00:03:51.190 --> 00:03:51.200 align:start position:0%
this could be done automatically. And
 

00:03:51.200 --> 00:03:52.710 align:start position:0%
this could be done automatically. And
so,<00:03:51.360><c> I</c><00:03:51.519><c> reached</c><00:03:51.760><c> out</c><00:03:51.840><c> to</c><00:03:52.000><c> Jacob</c><00:03:52.400><c> and</c><00:03:52.560><c> then</c>

00:03:52.710 --> 00:03:52.720 align:start position:0%
so, I reached out to Jacob and then
 

00:03:52.720 --> 00:03:54.869 align:start position:0%
so, I reached out to Jacob and then
Jacob<00:03:53.040><c> had</c><00:03:53.200><c> the</c><00:03:53.360><c> idea</c><00:03:53.920><c> to</c><00:03:54.159><c> make</c><00:03:54.319><c> this</c><00:03:54.480><c> into</c><00:03:54.720><c> a</c>

00:03:54.869 --> 00:03:54.879 align:start position:0%
Jacob had the idea to make this into a
 

00:03:54.879 --> 00:03:57.110 align:start position:0%
Jacob had the idea to make this into a
SAS<00:03:55.200><c> product</c><00:03:55.519><c> instead</c><00:03:55.840><c> of</c><00:03:56.000><c> an</c><00:03:56.239><c> internal</c><00:03:56.640><c> tool</c>

00:03:57.110 --> 00:03:57.120 align:start position:0%
SAS product instead of an internal tool
 

00:03:57.120 --> 00:03:58.710 align:start position:0%
SAS product instead of an internal tool
so<00:03:57.280><c> we</c><00:03:57.519><c> could</c><00:03:57.680><c> make</c><00:03:57.840><c> channels</c><00:03:58.239><c> together.</c>

00:03:58.710 --> 00:03:58.720 align:start position:0%
so we could make channels together.
 

00:03:58.720 --> 00:04:00.789 align:start position:0%
so we could make channels together.
Looking<00:03:59.040><c> back,</c><00:03:59.360><c> I'm</c><00:03:59.599><c> really</c><00:03:59.840><c> glad</c><00:04:00.080><c> that</c><00:04:00.319><c> I</c><00:04:00.640><c> let</c>

00:04:00.789 --> 00:04:00.799 align:start position:0%
Looking back, I'm really glad that I let
 

00:04:00.799 --> 00:04:03.670 align:start position:0%
Looking back, I'm really glad that I let
the<00:04:01.040><c> algorithm</c><00:04:01.599><c> guide</c><00:04:01.840><c> me.</c><00:04:02.560><c> And</c><00:04:03.040><c> you</c><00:04:03.200><c> know,</c><00:04:03.439><c> we</c>

00:04:03.670 --> 00:04:03.680 align:start position:0%
the algorithm guide me. And you know, we
 

00:04:03.680 --> 00:04:05.830 align:start position:0%
the algorithm guide me. And you know, we
all<00:04:03.920><c> are</c><00:04:04.080><c> on</c><00:04:04.239><c> Tik</c><00:04:04.480><c> Tok</c><00:04:04.640><c> and</c><00:04:04.879><c> Instagram</c><00:04:05.360><c> reels</c>

00:04:05.830 --> 00:04:05.840 align:start position:0%
all are on Tik Tok and Instagram reels
 

00:04:05.840 --> 00:04:07.830 align:start position:0%
all are on Tik Tok and Instagram reels
and<00:04:06.159><c> we</c><00:04:06.400><c> see</c><00:04:06.480><c> these</c><00:04:06.799><c> trends</c><00:04:06.959><c> in</c><00:04:07.200><c> videos</c><00:04:07.599><c> and</c>

00:04:07.830 --> 00:04:07.840 align:start position:0%
and we see these trends in videos and
 

00:04:07.840 --> 00:04:09.990 align:start position:0%
and we see these trends in videos and
most<00:04:08.000><c> of</c><00:04:08.080><c> the</c><00:04:08.159><c> time</c><00:04:08.319><c> we</c><00:04:08.480><c> scroll</c><00:04:08.879><c> past.</c><00:04:09.519><c> But</c><00:04:09.680><c> if</c>

00:04:09.990 --> 00:04:10.000 align:start position:0%
most of the time we scroll past. But if
 

00:04:10.000 --> 00:04:12.070 align:start position:0%
most of the time we scroll past. But if
you<00:04:10.400><c> take</c><00:04:10.640><c> a</c><00:04:10.879><c> second</c><00:04:11.040><c> to</c><00:04:11.280><c> actually</c><00:04:11.599><c> analyze</c>

00:04:12.070 --> 00:04:12.080 align:start position:0%
you take a second to actually analyze
 

00:04:12.080 --> 00:04:13.830 align:start position:0%
you take a second to actually analyze
what's<00:04:12.400><c> going</c><00:04:12.560><c> on,</c><00:04:13.040><c> there</c><00:04:13.280><c> are</c><00:04:13.439><c> plenty</c><00:04:13.680><c> of</c>

00:04:13.830 --> 00:04:13.840 align:start position:0%
what's going on, there are plenty of
 

00:04:13.840 --> 00:04:15.670 align:start position:0%
what's going on, there are plenty of
good<00:04:14.000><c> business</c><00:04:14.319><c> ideas</c><00:04:14.640><c> in</c><00:04:14.879><c> there.</c><00:04:15.280><c> So</c><00:04:15.439><c> you</c>

00:04:15.670 --> 00:04:15.680 align:start position:0%
good business ideas in there. So you
 

00:04:15.680 --> 00:04:17.590 align:start position:0%
good business ideas in there. So you
have<00:04:15.760><c> the</c><00:04:16.000><c> idea,</c><00:04:16.720><c> now</c><00:04:16.959><c> it's</c><00:04:17.120><c> time</c><00:04:17.280><c> to</c><00:04:17.440><c> get</c>

00:04:17.590 --> 00:04:17.600 align:start position:0%
have the idea, now it's time to get
 

00:04:17.600 --> 00:04:19.670 align:start position:0%
have the idea, now it's time to get
building.<00:04:18.160><c> Tell</c><00:04:18.400><c> me</c><00:04:18.560><c> how</c><00:04:18.880><c> how</c><00:04:19.040><c> did</c><00:04:19.199><c> you</c><00:04:19.359><c> build</c>

00:04:19.670 --> 00:04:19.680 align:start position:0%
building. Tell me how how did you build
 

00:04:19.680 --> 00:04:21.990 align:start position:0%
building. Tell me how how did you build
Faceless<00:04:20.160><c> Video?</c><00:04:20.959><c> It</c><00:04:21.199><c> started</c><00:04:21.440><c> with</c><00:04:21.600><c> Bubble</c>

00:04:21.990 --> 00:04:22.000 align:start position:0%
Faceless Video? It started with Bubble
 

00:04:22.000 --> 00:04:23.830 align:start position:0%
Faceless Video? It started with Bubble
and<00:04:22.240><c> APIs.</c><00:04:22.960><c> And</c><00:04:23.120><c> Bubble</c><00:04:23.520><c> was</c><00:04:23.680><c> the</c>

00:04:23.830 --> 00:04:23.840 align:start position:0%
and APIs. And Bubble was the
 

00:04:23.840 --> 00:04:25.189 align:start position:0%
and APIs. And Bubble was the
infrastructure<00:04:24.320><c> for</c><00:04:24.560><c> bringing</c><00:04:24.880><c> everything</c>

00:04:25.189 --> 00:04:25.199 align:start position:0%
infrastructure for bringing everything
 

00:04:25.199 --> 00:04:27.189 align:start position:0%
infrastructure for bringing everything
together.<00:04:25.840><c> And</c><00:04:26.160><c> like</c><00:04:26.400><c> with</c><00:04:26.560><c> anything</c><00:04:26.880><c> else,</c>

00:04:27.189 --> 00:04:27.199 align:start position:0%
together. And like with anything else,
 

00:04:27.199 --> 00:04:29.110 align:start position:0%
together. And like with anything else,
it<00:04:27.360><c> always</c><00:04:27.520><c> starts</c><00:04:27.759><c> with</c><00:04:28.000><c> can</c><00:04:28.240><c> this</c><00:04:28.479><c> work</c><00:04:28.960><c> from</c>

00:04:29.110 --> 00:04:29.120 align:start position:0%
it always starts with can this work from
 

00:04:29.120 --> 00:04:30.710 align:start position:0%
it always starts with can this work from
a<00:04:29.360><c> technical</c><00:04:29.759><c> perspective</c><00:04:30.160><c> at</c><00:04:30.240><c> least.</c><00:04:30.639><c> You</c>

00:04:30.710 --> 00:04:30.720 align:start position:0%
a technical perspective at least. You
 

00:04:30.720 --> 00:04:32.710 align:start position:0%
a technical perspective at least. You
know,<00:04:30.880><c> can</c><00:04:31.040><c> I</c><00:04:31.280><c> actually</c><00:04:31.919><c> find</c><00:04:32.080><c> a</c><00:04:32.240><c> way</c><00:04:32.320><c> to</c><00:04:32.479><c> type</c>

00:04:32.710 --> 00:04:32.720 align:start position:0%
know, can I actually find a way to type
 

00:04:32.720 --> 00:04:35.590 align:start position:0%
know, can I actually find a way to type
in<00:04:32.960><c> text</c><00:04:33.360><c> and</c><00:04:34.080><c> have</c><00:04:34.320><c> this</c><00:04:34.479><c> web</c><00:04:34.720><c> app</c><00:04:35.040><c> output</c><00:04:35.440><c> a</c>

00:04:35.590 --> 00:04:35.600 align:start position:0%
in text and have this web app output a
 

00:04:35.600 --> 00:04:37.350 align:start position:0%
in text and have this web app output a
faceless<00:04:36.080><c> video.</c><00:04:36.479><c> So</c><00:04:36.720><c> for</c><00:04:36.800><c> the</c><00:04:36.960><c> first</c><00:04:37.120><c> month,</c>

00:04:37.350 --> 00:04:37.360 align:start position:0%
faceless video. So for the first month,
 

00:04:37.360 --> 00:04:39.030 align:start position:0%
faceless video. So for the first month,
that's<00:04:37.520><c> really</c><00:04:37.680><c> all</c><00:04:37.840><c> this</c><00:04:38.000><c> MVP</c><00:04:38.479><c> was.</c><00:04:38.720><c> And</c><00:04:38.880><c> that</c>

00:04:39.030 --> 00:04:39.040 align:start position:0%
that's really all this MVP was. And that
 

00:04:39.040 --> 00:04:41.350 align:start position:0%
that's really all this MVP was. And that
eventually<00:04:39.440><c> migrated</c><00:04:40.000><c> into</c><00:04:40.479><c> managing</c><00:04:40.960><c> users,</c>

00:04:41.350 --> 00:04:41.360 align:start position:0%
eventually migrated into managing users,
 

00:04:41.360 --> 00:04:43.830 align:start position:0%
eventually migrated into managing users,
having<00:04:41.520><c> a</c><00:04:41.680><c> UIUX</c><00:04:42.320><c> flow,</c><00:04:42.800><c> a</c><00:04:42.960><c> payment</c><00:04:43.280><c> processing</c>

00:04:43.830 --> 00:04:43.840 align:start position:0%
having a UIUX flow, a payment processing
 

00:04:43.840 --> 00:04:46.310 align:start position:0%
having a UIUX flow, a payment processing
system,<00:04:44.320><c> a</c><00:04:44.560><c> pricing</c><00:04:44.880><c> model,</c><00:04:45.600><c> and</c><00:04:45.919><c> turning</c><00:04:46.160><c> it</c>

00:04:46.310 --> 00:04:46.320 align:start position:0%
system, a pricing model, and turning it
 

00:04:46.320 --> 00:04:48.310 align:start position:0%
system, a pricing model, and turning it
into<00:04:46.479><c> a</c><00:04:46.560><c> full-fledged</c><00:04:47.040><c> web</c><00:04:47.280><c> app.</c><00:04:47.840><c> Okay.</c><00:04:48.160><c> So</c>

00:04:48.310 --> 00:04:48.320 align:start position:0%
into a full-fledged web app. Okay. So
 

00:04:48.320 --> 00:04:49.990 align:start position:0%
into a full-fledged web app. Okay. So
tell<00:04:48.479><c> me</c><00:04:48.639><c> why</c><00:04:49.120><c> Bubble</c><00:04:49.520><c> is</c><00:04:49.680><c> great</c><00:04:49.840><c> for</c>

00:04:49.990 --> 00:04:50.000 align:start position:0%
tell me why Bubble is great for
 

00:04:50.000 --> 00:04:52.790 align:start position:0%
tell me why Bubble is great for
non-developers.<00:04:51.199><c> In</c><00:04:51.440><c> its</c><00:04:51.600><c> simplest</c><00:04:52.000><c> form,</c><00:04:52.479><c> it</c>

00:04:52.790 --> 00:04:52.800 align:start position:0%
non-developers. In its simplest form, it
 

00:04:52.800 --> 00:04:54.550 align:start position:0%
non-developers. In its simplest form, it
translates<00:04:53.440><c> programming</c><00:04:53.919><c> into</c><00:04:54.320><c> human</c>

00:04:54.550 --> 00:04:54.560 align:start position:0%
translates programming into human
 

00:04:54.560 --> 00:04:56.710 align:start position:0%
translates programming into human
language.<00:04:55.280><c> So</c><00:04:55.520><c> you</c><00:04:55.759><c> don't</c><00:04:55.919><c> need</c><00:04:56.000><c> to</c><00:04:56.240><c> know</c><00:04:56.479><c> how</c>

00:04:56.710 --> 00:04:56.720 align:start position:0%
language. So you don't need to know how
 

00:04:56.720 --> 00:04:58.310 align:start position:0%
language. So you don't need to know how
to<00:04:56.960><c> actually</c><00:04:57.360><c> code.</c><00:04:57.759><c> You</c><00:04:57.840><c> don't</c><00:04:58.000><c> need</c><00:04:58.080><c> to</c><00:04:58.160><c> know</c>

00:04:58.310 --> 00:04:58.320 align:start position:0%
to actually code. You don't need to know
 

00:04:58.320 --> 00:05:01.350 align:start position:0%
to actually code. You don't need to know
code<00:04:58.639><c> syntax</c><00:04:59.280><c> is</c><00:04:59.520><c> plain</c><00:04:59.759><c> English.</c><00:05:00.400><c> And</c><00:05:01.040><c> that's</c>

00:05:01.350 --> 00:05:01.360 align:start position:0%
code syntax is plain English. And that's
 

00:05:01.360 --> 00:05:03.030 align:start position:0%
code syntax is plain English. And that's
the<00:05:01.520><c> best</c><00:05:01.680><c> part</c><00:05:01.840><c> of</c><00:05:02.000><c> it</c><00:05:02.160><c> is</c><00:05:02.400><c> anyone</c><00:05:02.720><c> can</c><00:05:02.880><c> get</c>

00:05:03.030 --> 00:05:03.040 align:start position:0%
the best part of it is anyone can get
 

00:05:03.040 --> 00:05:04.550 align:start position:0%
the best part of it is anyone can get
started<00:05:03.360><c> and</c><00:05:03.680><c> understand</c><00:05:04.000><c> the</c><00:05:04.240><c> basic</c>

00:05:04.550 --> 00:05:04.560 align:start position:0%
started and understand the basic
 

00:05:04.560 --> 00:05:06.790 align:start position:0%
started and understand the basic
frameworks<00:05:05.040><c> even</c><00:05:05.360><c> of</c><00:05:06.000><c> programming.</c><00:05:06.639><c> Because</c>

00:05:06.790 --> 00:05:06.800 align:start position:0%
frameworks even of programming. Because
 

00:05:06.800 --> 00:05:08.310 align:start position:0%
frameworks even of programming. Because
now<00:05:06.960><c> that</c><00:05:07.120><c> I</c><00:05:07.280><c> actually</c><00:05:07.440><c> know</c><00:05:07.600><c> how</c><00:05:07.759><c> to</c><00:05:07.840><c> code,</c><00:05:08.160><c> I</c>

00:05:08.310 --> 00:05:08.320 align:start position:0%
now that I actually know how to code, I
 

00:05:08.320 --> 00:05:10.390 align:start position:0%
now that I actually know how to code, I
see<00:05:08.400><c> that</c><00:05:08.560><c> it</c><00:05:08.800><c> is</c><00:05:09.039><c> actually</c><00:05:09.440><c> still</c><00:05:09.840><c> very</c><00:05:10.160><c> much</c>

00:05:10.390 --> 00:05:10.400 align:start position:0%
see that it is actually still very much
 

00:05:10.400 --> 00:05:12.710 align:start position:0%
see that it is actually still very much
programming<00:05:11.199><c> just</c><00:05:11.360><c> in</c><00:05:11.520><c> a</c><00:05:11.600><c> more</c><00:05:11.759><c> visual</c><00:05:12.320><c> human</c>

00:05:12.710 --> 00:05:12.720 align:start position:0%
programming just in a more visual human
 

00:05:12.720 --> 00:05:14.550 align:start position:0%
programming just in a more visual human
friendly<00:05:12.960><c> way.</c><00:05:13.280><c> And</c><00:05:13.440><c> they</c><00:05:13.600><c> also</c><00:05:13.840><c> handle</c>

00:05:14.550 --> 00:05:14.560 align:start position:0%
friendly way. And they also handle
 

00:05:14.560 --> 00:05:16.230 align:start position:0%
friendly way. And they also handle
plenty<00:05:14.880><c> of</c><00:05:15.039><c> other</c><00:05:15.280><c> important</c><00:05:15.600><c> aspects</c><00:05:16.000><c> of</c>

00:05:16.230 --> 00:05:16.240 align:start position:0%
plenty of other important aspects of
 

00:05:16.240 --> 00:05:17.909 align:start position:0%
plenty of other important aspects of
actually<00:05:16.560><c> running</c><00:05:16.720><c> a</c><00:05:16.880><c> web</c><00:05:17.120><c> app</c><00:05:17.440><c> such</c><00:05:17.600><c> as</c>

00:05:17.909 --> 00:05:17.919 align:start position:0%
actually running a web app such as
 

00:05:17.919 --> 00:05:20.870 align:start position:0%
actually running a web app such as
security,<00:05:18.639><c> scalability,</c><00:05:19.759><c> privacy</c><00:05:20.240><c> rules,</c>

00:05:20.870 --> 00:05:20.880 align:start position:0%
security, scalability, privacy rules,
 

00:05:20.880 --> 00:05:22.790 align:start position:0%
security, scalability, privacy rules,
backend<00:05:21.440><c> management,</c><00:05:22.240><c> all</c><00:05:22.400><c> these</c><00:05:22.639><c> other</c>

00:05:22.790 --> 00:05:22.800 align:start position:0%
backend management, all these other
 

00:05:22.800 --> 00:05:25.029 align:start position:0%
backend management, all these other
things<00:05:22.960><c> that</c><00:05:23.280><c> I</c><00:05:23.600><c> would</c><00:05:23.759><c> not</c><00:05:23.919><c> have</c><00:05:24.160><c> even</c><00:05:24.800><c> known</c>

00:05:25.029 --> 00:05:25.039 align:start position:0%
things that I would not have even known
 

00:05:25.039 --> 00:05:26.950 align:start position:0%
things that I would not have even known
where<00:05:25.280><c> to</c><00:05:25.440><c> begin</c><00:05:25.919><c> if</c><00:05:26.160><c> I</c><00:05:26.320><c> was</c><00:05:26.479><c> just</c><00:05:26.639><c> coming</c><00:05:26.800><c> at</c>

00:05:26.950 --> 00:05:26.960 align:start position:0%
where to begin if I was just coming at
 

00:05:26.960 --> 00:05:28.950 align:start position:0%
where to begin if I was just coming at
it<00:05:27.199><c> completely</c><00:05:27.520><c> fresh</c><00:05:27.840><c> or</c><00:05:28.160><c> custom.</c><00:05:28.720><c> You</c><00:05:28.880><c> know,</c>

00:05:28.950 --> 00:05:28.960 align:start position:0%
it completely fresh or custom. You know,
 

00:05:28.960 --> 00:05:30.310 align:start position:0%
it completely fresh or custom. You know,
I<00:05:29.199><c> talked</c><00:05:29.280><c> to</c><00:05:29.360><c> a</c><00:05:29.520><c> lot</c><00:05:29.600><c> of</c><00:05:29.680><c> people</c><00:05:29.840><c> who</c><00:05:30.080><c> will</c>

00:05:30.310 --> 00:05:30.320 align:start position:0%
I talked to a lot of people who will
 

00:05:30.320 --> 00:05:32.469 align:start position:0%
I talked to a lot of people who will
start<00:05:30.479><c> their</c><00:05:30.639><c> app</c><00:05:30.880><c> on</c><00:05:31.039><c> Bubble,</c><00:05:31.440><c> but</c><00:05:31.680><c> then,</c><00:05:32.320><c> you</c>

00:05:32.469 --> 00:05:32.479 align:start position:0%
start their app on Bubble, but then, you
 

00:05:32.479 --> 00:05:34.469 align:start position:0%
start their app on Bubble, but then, you
know,<00:05:32.720><c> once</c><00:05:33.039><c> things</c><00:05:33.280><c> get</c><00:05:33.440><c> too</c><00:05:33.759><c> complicated</c>

00:05:34.469 --> 00:05:34.479 align:start position:0%
know, once things get too complicated
 

00:05:34.479 --> 00:05:35.990 align:start position:0%
know, once things get too complicated
and<00:05:34.720><c> they</c><00:05:34.960><c> really</c><00:05:35.120><c> want</c><00:05:35.280><c> to</c><00:05:35.440><c> scale,</c><00:05:35.759><c> they</c><00:05:35.919><c> have</c>

00:05:35.990 --> 00:05:36.000 align:start position:0%
and they really want to scale, they have
 

00:05:36.000 --> 00:05:37.430 align:start position:0%
and they really want to scale, they have
to<00:05:36.160><c> move</c><00:05:36.240><c> off</c><00:05:36.479><c> it,</c><00:05:36.639><c> which</c><00:05:36.880><c> ends</c><00:05:37.039><c> up</c><00:05:37.120><c> being</c><00:05:37.280><c> a</c>

00:05:37.430 --> 00:05:37.440 align:start position:0%
to move off it, which ends up being a
 

00:05:37.440 --> 00:05:39.909 align:start position:0%
to move off it, which ends up being a
huge<00:05:37.680><c> thing.</c><00:05:38.000><c> But</c><00:05:38.400><c> as</c><00:05:38.639><c> I</c><00:05:38.880><c> understand,</c><00:05:39.360><c> you</c>

00:05:39.909 --> 00:05:39.919 align:start position:0%
huge thing. But as I understand, you
 

00:05:39.919 --> 00:05:41.350 align:start position:0%
huge thing. But as I understand, you
your<00:05:40.240><c> app,</c><00:05:40.479><c> which</c><00:05:40.720><c> is</c><00:05:40.880><c> making</c><00:05:41.039><c> a</c><00:05:41.199><c> million</c>

00:05:41.350 --> 00:05:41.360 align:start position:0%
your app, which is making a million
 

00:05:41.360 --> 00:05:43.110 align:start position:0%
your app, which is making a million
dollars<00:05:41.520><c> a</c><00:05:41.680><c> year</c><00:05:41.840><c> right</c><00:05:42.000><c> now,</c><00:05:42.320><c> is</c><00:05:42.560><c> still</c><00:05:42.800><c> on</c>

00:05:43.110 --> 00:05:43.120 align:start position:0%
dollars a year right now, is still on
 

00:05:43.120 --> 00:05:45.749 align:start position:0%
dollars a year right now, is still on
Bubble.<00:05:43.759><c> Why</c><00:05:44.000><c> have</c><00:05:44.160><c> you</c><00:05:44.400><c> decided</c><00:05:44.800><c> to</c><00:05:45.120><c> stay</c><00:05:45.520><c> on</c>

00:05:45.749 --> 00:05:45.759 align:start position:0%
Bubble. Why have you decided to stay on
 

00:05:45.759 --> 00:05:47.830 align:start position:0%
Bubble. Why have you decided to stay on
Bubble?<00:05:46.240><c> If</c><00:05:46.479><c> it</c><00:05:46.560><c> ain't</c><00:05:46.800><c> broke,</c><00:05:47.120><c> don't</c><00:05:47.280><c> fix</c><00:05:47.440><c> it.</c>

00:05:47.830 --> 00:05:47.840 align:start position:0%
Bubble? If it ain't broke, don't fix it.
 

00:05:47.840 --> 00:05:49.189 align:start position:0%
Bubble? If it ain't broke, don't fix it.
It's<00:05:48.080><c> kind</c><00:05:48.160><c> of</c><00:05:48.320><c> as</c><00:05:48.479><c> simple</c><00:05:48.639><c> as</c><00:05:48.800><c> that.</c><00:05:49.120><c> You</c>

00:05:49.189 --> 00:05:49.199 align:start position:0%
It's kind of as simple as that. You
 

00:05:49.199 --> 00:05:50.469 align:start position:0%
It's kind of as simple as that. You
know,<00:05:49.360><c> we</c><00:05:49.520><c> haven't</c><00:05:49.680><c> run</c><00:05:49.840><c> into</c><00:05:50.080><c> any</c><00:05:50.240><c> issues</c>

00:05:50.469 --> 00:05:50.479 align:start position:0%
know, we haven't run into any issues
 

00:05:50.479 --> 00:05:53.110 align:start position:0%
know, we haven't run into any issues
that<00:05:50.720><c> would</c><00:05:50.880><c> require</c><00:05:51.280><c> a</c><00:05:51.600><c> dramatic</c><00:05:52.000><c> shift.</c><00:05:52.639><c> I</c><00:05:52.800><c> I</c>

00:05:53.110 --> 00:05:53.120 align:start position:0%
that would require a dramatic shift. I I
 

00:05:53.120 --> 00:05:55.990 align:start position:0%
that would require a dramatic shift. I I
ran<00:05:53.280><c> into</c><00:05:53.440><c> a</c><00:05:53.680><c> scalability</c><00:05:54.400><c> issue</c><00:05:54.880><c> once,</c><00:05:55.360><c> but</c>

00:05:55.990 --> 00:05:56.000 align:start position:0%
ran into a scalability issue once, but
 

00:05:56.000 --> 00:05:58.230 align:start position:0%
ran into a scalability issue once, but
Bubble<00:05:56.400><c> support</c><00:05:56.720><c> fixed</c><00:05:56.960><c> it,</c><00:05:57.280><c> and</c><00:05:57.600><c> that</c><00:05:57.919><c> was</c>

00:05:58.230 --> 00:05:58.240 align:start position:0%
Bubble support fixed it, and that was
 

00:05:58.240 --> 00:05:59.510 align:start position:0%
Bubble support fixed it, and that was
really<00:05:58.479><c> good</c><00:05:58.639><c> piece</c><00:05:58.880><c> of</c><00:05:58.960><c> mind</c><00:05:59.120><c> for</c><00:05:59.360><c> staying</c>

00:05:59.510 --> 00:05:59.520 align:start position:0%
really good piece of mind for staying
 

00:05:59.520 --> 00:06:01.270 align:start position:0%
really good piece of mind for staying
longterm<00:05:59.919><c> with</c><00:06:00.080><c> them.</c><00:06:00.400><c> And</c><00:06:00.639><c> realistically,</c>

00:06:01.270 --> 00:06:01.280 align:start position:0%
longterm with them. And realistically,
 

00:06:01.280 --> 00:06:02.390 align:start position:0%
longterm with them. And realistically,
all<00:06:01.440><c> the</c><00:06:01.520><c> bells</c><00:06:01.840><c> and</c><00:06:01.919><c> whistles</c><00:06:02.240><c> that</c>

00:06:02.390 --> 00:06:02.400 align:start position:0%
all the bells and whistles that
 

00:06:02.400 --> 00:06:04.309 align:start position:0%
all the bells and whistles that
developers<00:06:02.880><c> get</c><00:06:03.039><c> through</c><00:06:03.280><c> custom</c><00:06:03.600><c> code</c><00:06:04.080><c> is</c>

00:06:04.309 --> 00:06:04.319 align:start position:0%
developers get through custom code is
 

00:06:04.319 --> 00:06:05.990 align:start position:0%
developers get through custom code is
not<00:06:04.479><c> necessary</c><00:06:04.880><c> for</c><00:06:05.120><c> building</c><00:06:05.360><c> a</c><00:06:05.600><c> successful</c>

00:06:05.990 --> 00:06:06.000 align:start position:0%
not necessary for building a successful
 

00:06:06.000 --> 00:06:07.909 align:start position:0%
not necessary for building a successful
business.<00:06:06.400><c> All</c><00:06:06.639><c> you</c><00:06:06.800><c> need</c><00:06:06.880><c> is</c><00:06:07.039><c> a</c><00:06:07.199><c> great</c><00:06:07.360><c> idea,</c>

00:06:07.909 --> 00:06:07.919 align:start position:0%
business. All you need is a great idea,
 

00:06:07.919 --> 00:06:10.070 align:start position:0%
business. All you need is a great idea,
a<00:06:08.160><c> functioning</c><00:06:08.720><c> product,</c><00:06:09.280><c> and</c><00:06:09.440><c> a</c><00:06:09.600><c> great</c><00:06:09.759><c> go</c><00:06:09.919><c> to</c>

00:06:10.070 --> 00:06:10.080 align:start position:0%
a functioning product, and a great go to
 

00:06:10.080 --> 00:06:11.749 align:start position:0%
a functioning product, and a great go to
market<00:06:10.319><c> strategy,</c><00:06:10.880><c> and</c><00:06:11.039><c> you</c><00:06:11.199><c> can</c><00:06:11.360><c> validate</c>

00:06:11.749 --> 00:06:11.759 align:start position:0%
market strategy, and you can validate
 

00:06:11.759 --> 00:06:14.070 align:start position:0%
market strategy, and you can validate
really<00:06:12.080><c> quickly</c><00:06:12.400><c> and</c><00:06:12.960><c> scale</c><00:06:13.280><c> based</c><00:06:13.440><c> on</c><00:06:13.600><c> that.</c>

00:06:14.070 --> 00:06:14.080 align:start position:0%
really quickly and scale based on that.
 

00:06:14.080 --> 00:06:15.510 align:start position:0%
really quickly and scale based on that.
All right,<00:06:14.400><c> let's</c><00:06:14.720><c> pause</c><00:06:14.960><c> for</c><00:06:15.199><c> a</c><00:06:15.360><c> quick</c>

00:06:15.510 --> 00:06:15.520 align:start position:0%
All right, let's pause for a quick
 

00:06:15.520 --> 00:06:17.830 align:start position:0%
All right, let's pause for a quick
moment<00:06:15.759><c> to</c><00:06:16.080><c> talk</c><00:06:16.240><c> about</c><00:06:16.639><c> AI.</c><00:06:17.360><c> Everyone's</c>

00:06:17.830 --> 00:06:17.840 align:start position:0%
moment to talk about AI. Everyone's
 

00:06:17.840 --> 00:06:20.230 align:start position:0%
moment to talk about AI. Everyone's
hyped<00:06:18.160><c> up</c><00:06:18.319><c> right</c><00:06:18.560><c> now</c><00:06:18.800><c> about</c><00:06:19.120><c> using</c><00:06:19.520><c> AI</c><00:06:20.000><c> to</c>

00:06:20.230 --> 00:06:20.240 align:start position:0%
hyped up right now about using AI to
 

00:06:20.240 --> 00:06:22.390 align:start position:0%
hyped up right now about using AI to
build<00:06:20.479><c> apps.</c><00:06:21.360><c> But</c><00:06:21.520><c> right</c><00:06:21.759><c> now,</c><00:06:22.000><c> I'm</c><00:06:22.160><c> seeing</c>

00:06:22.390 --> 00:06:22.400 align:start position:0%
build apps. But right now, I'm seeing
 

00:06:22.400 --> 00:06:24.469 align:start position:0%
build apps. But right now, I'm seeing
that<00:06:22.639><c> most</c><00:06:22.880><c> people</c><00:06:23.360><c> get</c><00:06:23.600><c> stuck</c><00:06:23.840><c> in</c><00:06:24.080><c> what</c><00:06:24.319><c> I</c>

00:06:24.469 --> 00:06:24.479 align:start position:0%
that most people get stuck in what I
 

00:06:24.479 --> 00:06:27.029 align:start position:0%
that most people get stuck in what I
call<00:06:24.800><c> a</c><00:06:25.039><c> death</c><00:06:25.440><c> spiral</c><00:06:25.919><c> of</c><00:06:26.160><c> prompting.</c><00:06:26.800><c> They</c>

00:06:27.029 --> 00:06:27.039 align:start position:0%
call a death spiral of prompting. They
 

00:06:27.039 --> 00:06:28.870 align:start position:0%
call a death spiral of prompting. They
have<00:06:27.120><c> lots</c><00:06:27.360><c> of</c><00:06:27.440><c> chats</c><00:06:27.680><c> with</c><00:06:27.840><c> the</c><00:06:28.000><c> AI,</c><00:06:28.400><c> but</c><00:06:28.639><c> the</c>

00:06:28.870 --> 00:06:28.880 align:start position:0%
have lots of chats with the AI, but the
 

00:06:28.880 --> 00:06:31.430 align:start position:0%
have lots of chats with the AI, but the
code<00:06:29.199><c> never</c><00:06:29.600><c> actually</c><00:06:30.000><c> works.</c><00:06:30.800><c> And</c><00:06:31.120><c> that's</c>

00:06:31.430 --> 00:06:31.440 align:start position:0%
code never actually works. And that's
 

00:06:31.440 --> 00:06:33.270 align:start position:0%
code never actually works. And that's
exactly<00:06:31.919><c> why</c><00:06:32.160><c> we're</c><00:06:32.479><c> partnering</c><00:06:32.960><c> with</c>

00:06:33.270 --> 00:06:33.280 align:start position:0%
exactly why we're partnering with
 

00:06:33.280 --> 00:06:35.430 align:start position:0%
exactly why we're partnering with
Bubble.<00:06:33.840><c> Bubble</c><00:06:34.240><c> AI</c><00:06:34.560><c> doesn't</c><00:06:34.800><c> put</c><00:06:34.960><c> your</c><00:06:35.199><c> idea</c>

00:06:35.430 --> 00:06:35.440 align:start position:0%
Bubble. Bubble AI doesn't put your idea
 

00:06:35.440 --> 00:06:37.430 align:start position:0%
Bubble. Bubble AI doesn't put your idea
into<00:06:35.680><c> a</c><00:06:35.840><c> cookie</c><00:06:36.160><c> cutter</c><00:06:36.479><c> template.</c><00:06:37.199><c> It</c>

00:06:37.430 --> 00:06:37.440 align:start position:0%
into a cookie cutter template. It
 

00:06:37.440 --> 00:06:39.909 align:start position:0%
into a cookie cutter template. It
actually<00:06:37.759><c> blows</c><00:06:38.080><c> it</c><00:06:38.240><c> up</c><00:06:38.400><c> into</c><00:06:38.639><c> a</c><00:06:38.880><c> full</c><00:06:39.360><c> stack</c>

00:06:39.909 --> 00:06:39.919 align:start position:0%
actually blows it up into a full stack
 

00:06:39.919 --> 00:06:41.749 align:start position:0%
actually blows it up into a full stack
working<00:06:40.319><c> app.</c><00:06:40.880><c> All</c><00:06:41.039><c> you</c><00:06:41.199><c> have</c><00:06:41.280><c> to</c><00:06:41.360><c> do</c><00:06:41.520><c> is</c>

00:06:41.749 --> 00:06:41.759 align:start position:0%
working app. All you have to do is
 

00:06:41.759 --> 00:06:43.990 align:start position:0%
working app. All you have to do is
describe<00:06:42.080><c> your</c><00:06:42.319><c> idea</c><00:06:42.720><c> and</c><00:06:42.960><c> Bubble</c><00:06:43.360><c> AI</c><00:06:43.759><c> will</c>

00:06:43.990 --> 00:06:44.000 align:start position:0%
describe your idea and Bubble AI will
 

00:06:44.000 --> 00:06:46.710 align:start position:0%
describe your idea and Bubble AI will
spin<00:06:44.319><c> up</c><00:06:44.560><c> the</c><00:06:44.880><c> entire</c><00:06:45.280><c> stack</c><00:06:45.600><c> in</c><00:06:45.919><c> minutes.</c><00:06:46.479><c> A</c>

00:06:46.710 --> 00:06:46.720 align:start position:0%
spin up the entire stack in minutes. A
 

00:06:46.720 --> 00:06:50.309 align:start position:0%
spin up the entire stack in minutes. A
polished<00:06:47.199><c> UI,</c><00:06:48.080><c> a</c><00:06:48.319><c> pre-wired</c><00:06:48.960><c> database,</c><00:06:49.840><c> and</c>

00:06:50.309 --> 00:06:50.319 align:start position:0%
polished UI, a pre-wired database, and
 

00:06:50.319 --> 00:06:52.550 align:start position:0%
polished UI, a pre-wired database, and
workflows<00:06:50.960><c> that</c><00:06:51.199><c> are</c><00:06:51.360><c> ready</c><00:06:51.520><c> to</c><00:06:51.759><c> roll.</c><00:06:52.240><c> I've</c>

00:06:52.550 --> 00:06:52.560 align:start position:0%
workflows that are ready to roll. I've
 

00:06:52.560 --> 00:06:53.909 align:start position:0%
workflows that are ready to roll. I've
been<00:06:52.639><c> poking</c><00:06:52.960><c> around</c><00:06:53.120><c> with</c><00:06:53.199><c> it</c><00:06:53.360><c> to</c><00:06:53.600><c> build</c><00:06:53.759><c> some</c>

00:06:53.909 --> 00:06:53.919 align:start position:0%
been poking around with it to build some
 

00:06:53.919 --> 00:06:55.749 align:start position:0%
been poking around with it to build some
stuff<00:06:54.080><c> for</c><00:06:54.319><c> Starter</c><00:06:54.639><c> Story.</c><00:06:55.120><c> And</c><00:06:55.280><c> watching</c><00:06:55.600><c> it</c>

00:06:55.749 --> 00:06:55.759 align:start position:0%
stuff for Starter Story. And watching it
 

00:06:55.759 --> 00:06:57.510 align:start position:0%
stuff for Starter Story. And watching it
just<00:06:56.000><c> build</c><00:06:56.319><c> everything</c><00:06:56.720><c> while</c><00:06:56.960><c> I</c><00:06:57.120><c> sit</c><00:06:57.360><c> there</c>

00:06:57.510 --> 00:06:57.520 align:start position:0%
just build everything while I sit there
 

00:06:57.520 --> 00:06:59.749 align:start position:0%
just build everything while I sit there
and<00:06:57.680><c> enjoy</c><00:06:58.000><c> my</c><00:06:58.240><c> drink</c><00:06:58.960><c> is</c><00:06:59.120><c> pretty</c><00:06:59.360><c> amazing.</c>

00:06:59.749 --> 00:06:59.759 align:start position:0%
and enjoy my drink is pretty amazing.
 

00:06:59.759 --> 00:07:01.749 align:start position:0%
and enjoy my drink is pretty amazing.
So,<00:07:00.080><c> if</c><00:07:00.240><c> you've</c><00:07:00.400><c> been</c><00:07:00.560><c> sitting</c><00:07:00.720><c> on</c><00:07:00.960><c> an</c><00:07:01.120><c> idea,</c>

00:07:01.749 --> 00:07:01.759 align:start position:0%
So, if you've been sitting on an idea,
 

00:07:01.759 --> 00:07:02.870 align:start position:0%
So, if you've been sitting on an idea,
head<00:07:01.919><c> to</c><00:07:02.080><c> the</c><00:07:02.240><c> first</c><00:07:02.400><c> link</c><00:07:02.639><c> in</c><00:07:02.800><c> the</c>

00:07:02.870 --> 00:07:02.880 align:start position:0%
head to the first link in the
 

00:07:02.880 --> 00:07:05.749 align:start position:0%
head to the first link in the
description,<00:07:03.680><c> sign</c><00:07:03.919><c> up</c><00:07:04.160><c> for</c><00:07:04.400><c> free,</c><00:07:05.120><c> and</c><00:07:05.440><c> tell</c>

00:07:05.749 --> 00:07:05.759 align:start position:0%
description, sign up for free, and tell
 

00:07:05.759 --> 00:07:07.749 align:start position:0%
description, sign up for free, and tell
Bubble<00:07:06.240><c> AI</c><00:07:06.800><c> exactly</c><00:07:07.120><c> what</c><00:07:07.360><c> you</c><00:07:07.520><c> want</c><00:07:07.599><c> to</c>

00:07:07.749 --> 00:07:07.759 align:start position:0%
Bubble AI exactly what you want to
 

00:07:07.759 --> 00:07:10.070 align:start position:0%
Bubble AI exactly what you want to
build.<00:07:08.160><c> If</c><00:07:08.319><c> you</c><00:07:08.479><c> do</c><00:07:08.560><c> that,</c><00:07:09.039><c> drop</c><00:07:09.360><c> your</c><00:07:09.599><c> idea</c><00:07:09.919><c> in</c>

00:07:10.070 --> 00:07:10.080 align:start position:0%
build. If you do that, drop your idea in
 

00:07:10.080 --> 00:07:11.589 align:start position:0%
build. If you do that, drop your idea in
the<00:07:10.319><c> comments.</c><00:07:10.639><c> I'll</c><00:07:10.880><c> read</c><00:07:11.039><c> through</c><00:07:11.280><c> them</c><00:07:11.360><c> and</c>

00:07:11.589 --> 00:07:11.599 align:start position:0%
the comments. I'll read through them and
 

00:07:11.599 --> 00:07:12.950 align:start position:0%
the comments. I'll read through them and
let<00:07:11.680><c> you</c><00:07:11.840><c> know</c><00:07:11.919><c> which</c><00:07:12.080><c> ones</c><00:07:12.319><c> are</c><00:07:12.400><c> my</c><00:07:12.560><c> favorite.</c>

00:07:12.950 --> 00:07:12.960 align:start position:0%
let you know which ones are my favorite.
 

00:07:12.960 --> 00:07:14.870 align:start position:0%
let you know which ones are my favorite.
All right,<00:07:13.199><c> let's</c><00:07:13.520><c> dive</c><00:07:13.759><c> back</c><00:07:14.160><c> into</c><00:07:14.479><c> Jacob</c>

00:07:14.870 --> 00:07:14.880 align:start position:0%
All right, let's dive back into Jacob
 

00:07:14.880 --> 00:07:17.350 align:start position:0%
All right, let's dive back into Jacob
and<00:07:15.039><c> Alex</c><00:07:15.440><c> story.</c><00:07:15.919><c> On</c><00:07:16.160><c> that</c><00:07:16.400><c> note,</c><00:07:16.960><c> um,</c><00:07:17.199><c> what</c>

00:07:17.350 --> 00:07:17.360 align:start position:0%
and Alex story. On that note, um, what
 

00:07:17.360 --> 00:07:18.950 align:start position:0%
and Alex story. On that note, um, what
would<00:07:17.520><c> be</c><00:07:17.680><c> some</c><00:07:17.840><c> of</c><00:07:17.919><c> the</c><00:07:18.080><c> opportunities</c><00:07:18.639><c> that</c>

00:07:18.950 --> 00:07:18.960 align:start position:0%
would be some of the opportunities that
 

00:07:18.960 --> 00:07:21.670 align:start position:0%
would be some of the opportunities that
you<00:07:19.199><c> see</c><00:07:19.440><c> right</c><00:07:19.680><c> now</c><00:07:20.080><c> for</c><00:07:20.720><c> ideas</c><00:07:21.039><c> that</c><00:07:21.360><c> people</c>

00:07:21.670 --> 00:07:21.680 align:start position:0%
you see right now for ideas that people
 

00:07:21.680 --> 00:07:23.990 align:start position:0%
you see right now for ideas that people
could<00:07:22.000><c> build</c><00:07:22.400><c> with</c><00:07:22.720><c> Bubble</c><00:07:23.120><c> or</c><00:07:23.280><c> other</c><00:07:23.520><c> no</c><00:07:23.840><c> code</c>

00:07:23.990 --> 00:07:24.000 align:start position:0%
could build with Bubble or other no code
 

00:07:24.000 --> 00:07:25.909 align:start position:0%
could build with Bubble or other no code
tools?<00:07:24.639><c> I</c><00:07:24.880><c> think</c><00:07:24.960><c> marketplaces</c><00:07:25.520><c> are</c><00:07:25.759><c> kind</c><00:07:25.840><c> of</c>

00:07:25.909 --> 00:07:25.919 align:start position:0%
tools? I think marketplaces are kind of
 

00:07:25.919 --> 00:07:26.950 align:start position:0%
tools? I think marketplaces are kind of
the<00:07:26.000><c> first</c><00:07:26.160><c> thing</c><00:07:26.240><c> that</c><00:07:26.400><c> comes</c><00:07:26.560><c> to</c><00:07:26.720><c> mind</c>

00:07:26.950 --> 00:07:26.960 align:start position:0%
the first thing that comes to mind
 

00:07:26.960 --> 00:07:29.510 align:start position:0%
the first thing that comes to mind
actually.<00:07:27.360><c> Just</c><00:07:27.599><c> because</c><00:07:28.240><c> it's</c><00:07:28.639><c> the</c><00:07:29.120><c> easiest</c>

00:07:29.510 --> 00:07:29.520 align:start position:0%
actually. Just because it's the easiest
 

00:07:29.520 --> 00:07:31.189 align:start position:0%
actually. Just because it's the easiest
way<00:07:29.599><c> to</c><00:07:29.840><c> manage</c><00:07:30.000><c> a</c><00:07:30.240><c> front</c><00:07:30.400><c> end</c><00:07:30.560><c> and</c><00:07:30.720><c> a</c><00:07:30.880><c> backend</c>

00:07:31.189 --> 00:07:31.199 align:start position:0%
way to manage a front end and a backend
 

00:07:31.199 --> 00:07:32.629 align:start position:0%
way to manage a front end and a backend
through<00:07:31.440><c> no</c><00:07:31.759><c> code.</c><00:07:32.000><c> It's</c><00:07:32.160><c> a</c><00:07:32.319><c> little</c><00:07:32.319><c> bit</c><00:07:32.479><c> more</c>

00:07:32.629 --> 00:07:32.639 align:start position:0%
through no code. It's a little bit more
 

00:07:32.639 --> 00:07:34.469 align:start position:0%
through no code. It's a little bit more
generic,<00:07:33.120><c> but</c><00:07:33.360><c> honestly,</c><00:07:33.919><c> you</c><00:07:34.160><c> can</c><00:07:34.240><c> build</c>

00:07:34.469 --> 00:07:34.479 align:start position:0%
generic, but honestly, you can build
 

00:07:34.479 --> 00:07:36.070 align:start position:0%
generic, but honestly, you can build
anything<00:07:34.800><c> if</c><00:07:35.039><c> you</c><00:07:35.120><c> get</c><00:07:35.280><c> creative.</c><00:07:35.759><c> But</c><00:07:35.919><c> even</c>

00:07:36.070 --> 00:07:36.080 align:start position:0%
anything if you get creative. But even
 

00:07:36.080 --> 00:07:37.350 align:start position:0%
anything if you get creative. But even
if<00:07:36.240><c> it's</c><00:07:36.400><c> no</c><00:07:36.560><c> code,</c><00:07:36.960><c> that</c><00:07:37.120><c> doesn't</c>

00:07:37.350 --> 00:07:37.360 align:start position:0%
if it's no code, that doesn't
 

00:07:37.360 --> 00:07:39.189 align:start position:0%
if it's no code, that doesn't
necessarily<00:07:37.840><c> mean</c><00:07:38.000><c> that</c><00:07:38.160><c> it's</c><00:07:38.479><c> easy.</c><00:07:38.880><c> Uh</c><00:07:39.039><c> you</c>

00:07:39.189 --> 00:07:39.199 align:start position:0%
necessarily mean that it's easy. Uh you
 

00:07:39.199 --> 00:07:40.469 align:start position:0%
necessarily mean that it's easy. Uh you
still<00:07:39.360><c> have</c><00:07:39.440><c> to</c><00:07:39.599><c> get</c><00:07:39.680><c> creative.</c><00:07:40.080><c> You</c><00:07:40.240><c> still</c>

00:07:40.469 --> 00:07:40.479 align:start position:0%
still have to get creative. You still
 

00:07:40.479 --> 00:07:43.110 align:start position:0%
still have to get creative. You still
need<00:07:40.880><c> to</c><00:07:41.280><c> be</c><00:07:41.520><c> resourceful,</c><00:07:42.479><c> but</c><00:07:42.800><c> there's</c>

00:07:43.110 --> 00:07:43.120 align:start position:0%
need to be resourceful, but there's
 

00:07:43.120 --> 00:07:44.550 align:start position:0%
need to be resourceful, but there's
plenty<00:07:43.360><c> of</c><00:07:43.440><c> opportunities</c><00:07:43.840><c> with</c><00:07:44.080><c> no</c><00:07:44.240><c> code.</c>

00:07:44.550 --> 00:07:44.560 align:start position:0%
plenty of opportunities with no code.
 

00:07:44.560 --> 00:07:46.469 align:start position:0%
plenty of opportunities with no code.
And<00:07:44.639><c> it</c><00:07:44.880><c> brings</c><00:07:45.039><c> down</c><00:07:45.199><c> the</c><00:07:45.360><c> barrier</c><00:07:45.680><c> to</c><00:07:45.919><c> entry</c>

00:07:46.469 --> 00:07:46.479 align:start position:0%
And it brings down the barrier to entry
 

00:07:46.479 --> 00:07:48.950 align:start position:0%
And it brings down the barrier to entry
enough<00:07:46.800><c> so</c><00:07:47.039><c> that</c><00:07:47.360><c> you</c><00:07:47.599><c> can</c><00:07:48.080><c> start.</c><00:07:48.639><c> And</c>

00:07:48.950 --> 00:07:48.960 align:start position:0%
enough so that you can start. And
 

00:07:48.960 --> 00:07:51.350 align:start position:0%
enough so that you can start. And
starting<00:07:49.360><c> is</c><00:07:49.599><c> the</c><00:07:49.840><c> biggest</c><00:07:50.080><c> hurdle.</c><00:07:50.960><c> Well,</c>

00:07:51.350 --> 00:07:51.360 align:start position:0%
starting is the biggest hurdle. Well,
 

00:07:51.360 --> 00:07:53.110 align:start position:0%
starting is the biggest hurdle. Well,
that's<00:07:51.599><c> awesome.</c><00:07:52.160><c> So,</c><00:07:52.479><c> I</c><00:07:52.720><c> want</c><00:07:52.800><c> to</c><00:07:52.880><c> switch</c>

00:07:53.110 --> 00:07:53.120 align:start position:0%
that's awesome. So, I want to switch
 

00:07:53.120 --> 00:07:55.990 align:start position:0%
that's awesome. So, I want to switch
gears<00:07:53.440><c> to</c><00:07:53.919><c> marketing</c><00:07:54.479><c> distribution.</c><00:07:55.599><c> How</c><00:07:55.759><c> did</c>

00:07:55.990 --> 00:07:56.000 align:start position:0%
gears to marketing distribution. How did
 

00:07:56.000 --> 00:07:59.110 align:start position:0%
gears to marketing distribution. How did
you<00:07:56.319><c> take</c><00:07:56.560><c> this</c><00:07:56.879><c> idea</c><00:07:57.360><c> from</c><00:07:57.680><c> zero</c><00:07:58.479><c> to</c><00:07:58.960><c> a</c>

00:07:59.110 --> 00:07:59.120 align:start position:0%
you take this idea from zero to a
 

00:07:59.120 --> 00:08:01.430 align:start position:0%
you take this idea from zero to a
million<00:07:59.440><c> dollars</c><00:07:59.599><c> a</c><00:07:59.840><c> year?</c><00:08:00.720><c> To</c><00:08:01.039><c> have</c><00:08:01.199><c> success</c>

00:08:01.430 --> 00:08:01.440 align:start position:0%
million dollars a year? To have success
 

00:08:01.440 --> 00:08:04.550 align:start position:0%
million dollars a year? To have success
as<00:08:01.680><c> a</c><00:08:01.759><c> B2C</c><00:08:02.560><c> app,</c><00:08:03.120><c> you</c><00:08:03.440><c> need</c><00:08:03.599><c> to</c><00:08:03.840><c> have</c><00:08:04.160><c> viral</c>

00:08:04.550 --> 00:08:04.560 align:start position:0%
as a B2C app, you need to have viral
 

00:08:04.560 --> 00:08:06.710 align:start position:0%
as a B2C app, you need to have viral
potential.<00:08:05.360><c> Your</c><00:08:05.919><c> CAC,</c><00:08:06.400><c> customer</c>

00:08:06.710 --> 00:08:06.720 align:start position:0%
potential. Your CAC, customer
 

00:08:06.720 --> 00:08:09.029 align:start position:0%
potential. Your CAC, customer
acquisition<00:08:07.280><c> costs,</c><00:08:07.759><c> it's</c><00:08:08.080><c> pretty</c><00:08:08.319><c> low.</c><00:08:08.879><c> And</c>

00:08:09.029 --> 00:08:09.039 align:start position:0%
acquisition costs, it's pretty low. And
 

00:08:09.039 --> 00:08:11.430 align:start position:0%
acquisition costs, it's pretty low. And
so,<00:08:09.280><c> to</c><00:08:09.520><c> spend</c><00:08:10.080><c> a</c><00:08:10.400><c> lot</c><00:08:10.479><c> on</c><00:08:10.720><c> ads</c><00:08:11.039><c> or</c><00:08:11.199><c> other</c>

00:08:11.430 --> 00:08:11.440 align:start position:0%
so, to spend a lot on ads or other
 

00:08:11.440 --> 00:08:13.749 align:start position:0%
so, to spend a lot on ads or other
methods<00:08:11.680><c> of</c><00:08:11.840><c> promotion</c><00:08:12.639><c> is</c><00:08:13.280><c> not</c><00:08:13.520><c> really</c>

00:08:13.749 --> 00:08:13.759 align:start position:0%
methods of promotion is not really
 

00:08:13.759 --> 00:08:16.150 align:start position:0%
methods of promotion is not really
viable<00:08:14.240><c> for</c><00:08:14.960><c> a</c><00:08:15.039><c> a</c><00:08:15.199><c> B2C</c><00:08:15.599><c> app.</c><00:08:15.840><c> That's</c><00:08:16.000><c> something</c>

00:08:16.150 --> 00:08:16.160 align:start position:0%
viable for a a B2C app. That's something
 

00:08:16.160 --> 00:08:18.150 align:start position:0%
viable for a a B2C app. That's something
I<00:08:16.319><c> learned</c><00:08:16.720><c> with</c><00:08:16.879><c> Faces</c><00:08:17.520><c> that</c><00:08:17.759><c> my</c><00:08:18.000><c> other</c>

00:08:18.150 --> 00:08:18.160 align:start position:0%
I learned with Faces that my other
 

00:08:18.160 --> 00:08:19.749 align:start position:0%
I learned with Faces that my other
products<00:08:18.479><c> really</c><00:08:18.720><c> didn't</c><00:08:18.960><c> have.</c><00:08:19.360><c> We</c><00:08:19.599><c> didn't</c>

00:08:19.749 --> 00:08:19.759 align:start position:0%
products really didn't have. We didn't
 

00:08:19.759 --> 00:08:21.510 align:start position:0%
products really didn't have. We didn't
reinvent<00:08:20.160><c> the</c><00:08:20.319><c> wheel.</c><00:08:20.639><c> We</c><00:08:20.960><c> do</c><00:08:21.039><c> all</c><00:08:21.280><c> of</c><00:08:21.360><c> the</c>

00:08:21.510 --> 00:08:21.520 align:start position:0%
reinvent the wheel. We do all of the
 

00:08:21.520 --> 00:08:23.589 align:start position:0%
reinvent the wheel. We do all of the
usual<00:08:21.840><c> stuff,</c><00:08:22.160><c> you</c><00:08:22.319><c> know,</c><00:08:22.400><c> we</c><00:08:22.560><c> do</c><00:08:22.800><c> SEO,</c><00:08:23.280><c> we</c><00:08:23.440><c> do</c>

00:08:23.589 --> 00:08:23.599 align:start position:0%
usual stuff, you know, we do SEO, we do
 

00:08:23.599 --> 00:08:26.150 align:start position:0%
usual stuff, you know, we do SEO, we do
ads,<00:08:24.000><c> influencers,</c><00:08:24.800><c> organic</c><00:08:25.280><c> content,</c><00:08:26.000><c> but</c>

00:08:26.150 --> 00:08:26.160 align:start position:0%
ads, influencers, organic content, but
 

00:08:26.160 --> 00:08:28.070 align:start position:0%
ads, influencers, organic content, but
the<00:08:26.400><c> key</c><00:08:26.560><c> is</c><00:08:26.800><c> really</c><00:08:27.199><c> getting</c><00:08:27.440><c> your</c><00:08:27.680><c> messaging</c>

00:08:28.070 --> 00:08:28.080 align:start position:0%
the key is really getting your messaging
 

00:08:28.080 --> 00:08:30.390 align:start position:0%
the key is really getting your messaging
right<00:08:28.560><c> and</c><00:08:28.879><c> it's</c><00:08:29.120><c> nothing</c><00:08:29.440><c> fancy.</c><00:08:30.080><c> It's</c><00:08:30.240><c> just</c>

00:08:30.390 --> 00:08:30.400 align:start position:0%
right and it's nothing fancy. It's just
 

00:08:30.400 --> 00:08:33.029 align:start position:0%
right and it's nothing fancy. It's just
good<00:08:30.639><c> execution.</c><00:08:31.440><c> So,</c><00:08:31.680><c> I</c><00:08:32.000><c> come</c><00:08:32.159><c> from</c><00:08:32.479><c> a</c><00:08:32.800><c> film</c>

00:08:33.029 --> 00:08:33.039 align:start position:0%
good execution. So, I come from a film
 

00:08:33.039 --> 00:08:35.190 align:start position:0%
good execution. So, I come from a film
making<00:08:33.360><c> background</c><00:08:34.159><c> and</c><00:08:34.479><c> that's</c><00:08:34.719><c> a</c><00:08:34.880><c> lot</c><00:08:34.959><c> about</c>

00:08:35.190 --> 00:08:35.200 align:start position:0%
making background and that's a lot about
 

00:08:35.200 --> 00:08:36.949 align:start position:0%
making background and that's a lot about
storytelling.<00:08:36.000><c> So,</c><00:08:36.159><c> if</c><00:08:36.320><c> you</c><00:08:36.399><c> can</c><00:08:36.560><c> crack</c>

00:08:36.949 --> 00:08:36.959 align:start position:0%
storytelling. So, if you can crack
 

00:08:36.959 --> 00:08:39.029 align:start position:0%
storytelling. So, if you can crack
storytelling,<00:08:37.760><c> then</c><00:08:38.000><c> you</c><00:08:38.159><c> can</c><00:08:38.240><c> really</c><00:08:38.560><c> sell</c><00:08:38.800><c> a</c>

00:08:39.029 --> 00:08:39.039 align:start position:0%
storytelling, then you can really sell a
 

00:08:39.039 --> 00:08:40.709 align:start position:0%
storytelling, then you can really sell a
product.<00:08:39.599><c> What</c><00:08:39.760><c> you</c><00:08:39.919><c> want</c><00:08:40.000><c> to</c><00:08:40.159><c> do</c><00:08:40.399><c> is</c>

00:08:40.709 --> 00:08:40.719 align:start position:0%
product. What you want to do is
 

00:08:40.719 --> 00:08:43.589 align:start position:0%
product. What you want to do is
kickstart<00:08:41.519><c> telling</c><00:08:41.839><c> that</c><00:08:42.159><c> story</c><00:08:42.640><c> with</c><00:08:43.039><c> ads,</c>

00:08:43.589 --> 00:08:43.599 align:start position:0%
kickstart telling that story with ads,
 

00:08:43.599 --> 00:08:45.269 align:start position:0%
kickstart telling that story with ads,
influencer<00:08:44.159><c> collaborations,</c><00:08:44.880><c> and</c><00:08:45.040><c> other</c>

00:08:45.269 --> 00:08:45.279 align:start position:0%
influencer collaborations, and other
 

00:08:45.279 --> 00:08:47.269 align:start position:0%
influencer collaborations, and other
things.<00:08:45.600><c> That's</c><00:08:45.760><c> what</c><00:08:45.920><c> Jacob</c><00:08:46.240><c> and</c><00:08:46.399><c> I</c><00:08:46.560><c> did.</c><00:08:46.800><c> Our</c>

00:08:47.269 --> 00:08:47.279 align:start position:0%
things. That's what Jacob and I did. Our
 

00:08:47.279 --> 00:08:49.190 align:start position:0%
things. That's what Jacob and I did. Our
first<00:08:47.519><c> advertisement</c><00:08:48.160><c> for</c><00:08:48.399><c> Faceless</c><00:08:48.880><c> Video</c>

00:08:49.190 --> 00:08:49.200 align:start position:0%
first advertisement for Faceless Video
 

00:08:49.200 --> 00:08:51.190 align:start position:0%
first advertisement for Faceless Video
was<00:08:49.440><c> a</c><00:08:49.680><c> Twitter</c><00:08:49.920><c> thread</c><00:08:50.240><c> that</c><00:08:50.480><c> we</c><00:08:50.720><c> spent</c><00:08:51.040><c> I</c>

00:08:51.190 --> 00:08:51.200 align:start position:0%
was a Twitter thread that we spent I
 

00:08:51.200 --> 00:08:53.350 align:start position:0%
was a Twitter thread that we spent I
don't know<00:08:51.360><c> like</c><00:08:51.519><c> 200</c><00:08:51.920><c> bucks</c><00:08:52.160><c> on</c><00:08:52.560><c> and</c><00:08:52.959><c> we</c><00:08:53.200><c> got</c>

00:08:53.350 --> 00:08:53.360 align:start position:0%
don't know like 200 bucks on and we got
 

00:08:53.360 --> 00:08:55.350 align:start position:0%
don't know like 200 bucks on and we got
hundreds<00:08:53.680><c> of</c><00:08:53.839><c> thousands</c><00:08:54.160><c> of</c><00:08:54.320><c> views</c><00:08:54.720><c> and</c><00:08:55.200><c> kind</c>

00:08:55.350 --> 00:08:55.360 align:start position:0%
hundreds of thousands of views and kind
 

00:08:55.360 --> 00:08:57.829 align:start position:0%
hundreds of thousands of views and kind
of<00:08:55.440><c> instantly</c><00:08:55.920><c> went</c><00:08:56.160><c> viral.</c><00:08:56.959><c> Um,</c><00:08:57.440><c> and</c><00:08:57.680><c> that</c>

00:08:57.829 --> 00:08:57.839 align:start position:0%
of instantly went viral. Um, and that
 

00:08:57.839 --> 00:08:59.829 align:start position:0%
of instantly went viral. Um, and that
was<00:08:57.920><c> very</c><00:08:58.160><c> very</c><00:08:58.399><c> helpful</c><00:08:58.640><c> for</c><00:08:58.880><c> us.</c><00:08:59.279><c> And</c><00:08:59.440><c> also</c>

00:08:59.829 --> 00:08:59.839 align:start position:0%
was very very helpful for us. And also
 

00:08:59.839 --> 00:09:01.910 align:start position:0%
was very very helpful for us. And also
our<00:09:00.080><c> biggest</c><00:09:00.399><c> growth</c><00:09:00.720><c> came</c><00:09:00.959><c> when</c><00:09:01.200><c> influencers</c>

00:09:01.910 --> 00:09:01.920 align:start position:0%
our biggest growth came when influencers
 

00:09:01.920 --> 00:09:03.750 align:start position:0%
our biggest growth came when influencers
who<00:09:02.160><c> saw</c><00:09:02.320><c> our</c><00:09:02.560><c> ads</c><00:09:02.880><c> that</c><00:09:03.120><c> we'd</c><00:09:03.360><c> never</c><00:09:03.519><c> even</c>

00:09:03.750 --> 00:09:03.760 align:start position:0%
who saw our ads that we'd never even
 

00:09:03.760 --> 00:09:05.509 align:start position:0%
who saw our ads that we'd never even
reached<00:09:04.000><c> out</c><00:09:04.160><c> to</c><00:09:04.720><c> started</c><00:09:05.120><c> promoting</c>

00:09:05.509 --> 00:09:05.519 align:start position:0%
reached out to started promoting
 

00:09:05.519 --> 00:09:07.829 align:start position:0%
reached out to started promoting
Faceless.video<00:09:06.560><c> on</c><00:09:06.720><c> their</c><00:09:06.880><c> own</c><00:09:07.279><c> because</c><00:09:07.600><c> they</c>

00:09:07.829 --> 00:09:07.839 align:start position:0%
Faceless.video on their own because they
 

00:09:07.839 --> 00:09:09.350 align:start position:0%
Faceless.video on their own because they
genuinely<00:09:08.399><c> believe</c><00:09:08.640><c> that</c><00:09:08.880><c> their</c><00:09:09.040><c> audience</c>

00:09:09.350 --> 00:09:09.360 align:start position:0%
genuinely believe that their audience
 

00:09:09.360 --> 00:09:11.750 align:start position:0%
genuinely believe that their audience
would<00:09:09.519><c> like</c><00:09:09.760><c> the</c><00:09:09.920><c> product.</c><00:09:10.480><c> And</c><00:09:10.880><c> Jacob</c><00:09:11.440><c> also</c>

00:09:11.750 --> 00:09:11.760 align:start position:0%
would like the product. And Jacob also
 

00:09:11.760 --> 00:09:13.269 align:start position:0%
would like the product. And Jacob also
knows<00:09:11.920><c> this,</c><00:09:12.080><c> but</c><00:09:12.320><c> word</c><00:09:12.560><c> of</c><00:09:12.720><c> mouth</c><00:09:13.040><c> is</c>

00:09:13.269 --> 00:09:13.279 align:start position:0%
knows this, but word of mouth is
 

00:09:13.279 --> 00:09:15.110 align:start position:0%
knows this, but word of mouth is
consistently<00:09:14.000><c> one</c><00:09:14.240><c> of</c><00:09:14.320><c> our</c><00:09:14.560><c> top</c><00:09:14.800><c> five</c>

00:09:15.110 --> 00:09:15.120 align:start position:0%
consistently one of our top five
 

00:09:15.120 --> 00:09:17.430 align:start position:0%
consistently one of our top five
attribution<00:09:15.760><c> sources.</c><00:09:16.560><c> So</c><00:09:16.800><c> like</c><00:09:16.959><c> on</c><00:09:17.120><c> our</c>

00:09:17.430 --> 00:09:17.440 align:start position:0%
attribution sources. So like on our
 

00:09:17.440 --> 00:09:18.870 align:start position:0%
attribution sources. So like on our
customer<00:09:17.760><c> attribution</c><00:09:18.240><c> thing,</c><00:09:18.560><c> people</c><00:09:18.720><c> will</c>

00:09:18.870 --> 00:09:18.880 align:start position:0%
customer attribution thing, people will
 

00:09:18.880 --> 00:09:20.790 align:start position:0%
customer attribution thing, people will
just<00:09:19.040><c> type</c><00:09:19.200><c> in</c><00:09:19.440><c> friend.</c><00:09:19.920><c> So</c><00:09:20.160><c> you</c><00:09:20.320><c> know,</c><00:09:20.560><c> people</c>

00:09:20.790 --> 00:09:20.800 align:start position:0%
just type in friend. So you know, people
 

00:09:20.800 --> 00:09:22.550 align:start position:0%
just type in friend. So you know, people
actually<00:09:21.040><c> like</c><00:09:21.279><c> the</c><00:09:21.519><c> product.</c><00:09:22.080><c> But</c><00:09:22.240><c> if</c><00:09:22.399><c> you</c>

00:09:22.550 --> 00:09:22.560 align:start position:0%
actually like the product. But if you
 

00:09:22.560 --> 00:09:24.310 align:start position:0%
actually like the product. But if you
have<00:09:22.720><c> something</c><00:09:22.959><c> that's</c><00:09:23.279><c> truly</c><00:09:23.600><c> original</c><00:09:24.000><c> and</c>

00:09:24.310 --> 00:09:24.320 align:start position:0%
have something that's truly original and
 

00:09:24.320 --> 00:09:26.310 align:start position:0%
have something that's truly original and
solves<00:09:24.640><c> a</c><00:09:24.800><c> pain</c><00:09:25.040><c> point,</c><00:09:25.519><c> especially</c><00:09:26.000><c> around</c>

00:09:26.310 --> 00:09:26.320 align:start position:0%
solves a pain point, especially around
 

00:09:26.320 --> 00:09:28.710 align:start position:0%
solves a pain point, especially around
an<00:09:26.560><c> emerging</c><00:09:26.880><c> trend,</c><00:09:27.519><c> people</c><00:09:27.839><c> will</c><00:09:28.080><c> notice</c><00:09:28.480><c> if</c>

00:09:28.710 --> 00:09:28.720 align:start position:0%
an emerging trend, people will notice if
 

00:09:28.720 --> 00:09:30.389 align:start position:0%
an emerging trend, people will notice if
you're<00:09:28.880><c> first</c><00:09:29.120><c> to</c><00:09:29.279><c> market</c><00:09:29.519><c> with</c><00:09:29.760><c> something.</c>

00:09:30.389 --> 00:09:30.399 align:start position:0%
you're first to market with something.
 

00:09:30.399 --> 00:09:32.790 align:start position:0%
you're first to market with something.
So<00:09:30.640><c> that's</c><00:09:30.880><c> what</c><00:09:31.040><c> we</c><00:09:31.600><c> kind</c><00:09:31.839><c> of</c><00:09:32.160><c> centered</c><00:09:32.560><c> our</c>

00:09:32.790 --> 00:09:32.800 align:start position:0%
So that's what we kind of centered our
 

00:09:32.800 --> 00:09:34.790 align:start position:0%
So that's what we kind of centered our
whole<00:09:33.120><c> marketing</c><00:09:33.519><c> strategy</c><00:09:34.000><c> around</c><00:09:34.480><c> is</c>

00:09:34.790 --> 00:09:34.800 align:start position:0%
whole marketing strategy around is
 

00:09:34.800 --> 00:09:37.269 align:start position:0%
whole marketing strategy around is
showing<00:09:35.279><c> how</c><00:09:35.440><c> are</c><00:09:35.600><c> we</c><00:09:35.839><c> different</c><00:09:36.320><c> from</c><00:09:36.880><c> what's</c>

00:09:37.269 --> 00:09:37.279 align:start position:0%
showing how are we different from what's
 

00:09:37.279 --> 00:09:39.910 align:start position:0%
showing how are we different from what's
out<00:09:37.440><c> there?</c><00:09:38.240><c> And</c><00:09:38.480><c> then</c><00:09:38.720><c> also</c><00:09:39.279><c> this</c><00:09:39.519><c> is</c><00:09:39.600><c> how</c><00:09:39.760><c> you</c>

00:09:39.910 --> 00:09:39.920 align:start position:0%
out there? And then also this is how you
 

00:09:39.920 --> 00:09:41.829 align:start position:0%
out there? And then also this is how you
can<00:09:40.000><c> tap</c><00:09:40.240><c> into</c><00:09:40.480><c> this</c><00:09:40.720><c> existing</c><00:09:41.120><c> trend</c><00:09:41.440><c> in</c><00:09:41.680><c> a</c>

00:09:41.829 --> 00:09:41.839 align:start position:0%
can tap into this existing trend in a
 

00:09:41.839 --> 00:09:43.910 align:start position:0%
can tap into this existing trend in a
new<00:09:42.000><c> way.</c><00:09:42.480><c> So</c><00:09:42.720><c> you</c><00:09:42.959><c> can</c><00:09:43.200><c> lay</c><00:09:43.360><c> the</c><00:09:43.519><c> foundation</c>

00:09:43.910 --> 00:09:43.920 align:start position:0%
new way. So you can lay the foundation
 

00:09:43.920 --> 00:09:45.910 align:start position:0%
new way. So you can lay the foundation
with<00:09:44.240><c> traditional</c><00:09:44.720><c> marketing</c><00:09:45.200><c> like</c><00:09:45.519><c> ads,</c>

00:09:45.910 --> 00:09:45.920 align:start position:0%
with traditional marketing like ads,
 

00:09:45.920 --> 00:09:47.750 align:start position:0%
with traditional marketing like ads,
influencers<00:09:46.480><c> and</c><00:09:46.640><c> all</c><00:09:46.720><c> these</c><00:09:46.959><c> things.</c><00:09:47.519><c> But</c>

00:09:47.750 --> 00:09:47.760 align:start position:0%
influencers and all these things. But
 

00:09:47.760 --> 00:09:49.509 align:start position:0%
influencers and all these things. But
virality<00:09:48.399><c> happens</c><00:09:48.640><c> when</c><00:09:48.880><c> the</c><00:09:49.120><c> product</c><00:09:49.360><c> kind</c>

00:09:49.509 --> 00:09:49.519 align:start position:0%
virality happens when the product kind
 

00:09:49.519 --> 00:09:51.030 align:start position:0%
virality happens when the product kind
of<00:09:49.680><c> speaks</c><00:09:49.920><c> for</c><00:09:50.080><c> itself</c><00:09:50.399><c> and</c><00:09:50.640><c> you</c><00:09:50.800><c> have</c><00:09:50.880><c> the</c>

00:09:51.030 --> 00:09:51.040 align:start position:0%
of speaks for itself and you have the
 

00:09:51.040 --> 00:09:52.550 align:start position:0%
of speaks for itself and you have the
proper<00:09:51.279><c> messaging</c><00:09:51.760><c> and</c><00:09:52.080><c> all</c><00:09:52.240><c> of</c><00:09:52.399><c> the</c>

00:09:52.550 --> 00:09:52.560 align:start position:0%
proper messaging and all of the
 

00:09:52.560 --> 00:09:54.070 align:start position:0%
proper messaging and all of the
incentives<00:09:52.959><c> are</c><00:09:53.120><c> lined</c><00:09:53.360><c> up.</c><00:09:53.760><c> I</c><00:09:53.920><c> want</c><00:09:54.000><c> to</c>

00:09:54.070 --> 00:09:54.080 align:start position:0%
incentives are lined up. I want to
 

00:09:54.080 --> 00:09:56.230 align:start position:0%
incentives are lined up. I want to
switch<00:09:54.240><c> gears</c><00:09:54.560><c> a</c><00:09:54.720><c> little</c><00:09:54.800><c> bit</c><00:09:54.959><c> and</c><00:09:55.279><c> talk</c><00:09:55.519><c> about</c>

00:09:56.230 --> 00:09:56.240 align:start position:0%
switch gears a little bit and talk about
 

00:09:56.240 --> 00:09:58.150 align:start position:0%
switch gears a little bit and talk about
uh<00:09:56.320><c> this</c><00:09:56.560><c> business</c><00:09:56.880><c> that</c><00:09:57.120><c> you</c><00:09:57.279><c> built</c><00:09:57.600><c> and</c><00:09:57.920><c> how</c>

00:09:58.150 --> 00:09:58.160 align:start position:0%
uh this business that you built and how
 

00:09:58.160 --> 00:10:00.150 align:start position:0%
uh this business that you built and how
it's<00:09:58.399><c> impacted</c><00:09:58.800><c> you.</c><00:09:59.360><c> Uh</c><00:09:59.600><c> you</c><00:09:59.760><c> had</c><00:09:59.839><c> a</c><00:09:59.920><c> lot</c><00:10:00.000><c> of</c>

00:10:00.150 --> 00:10:00.160 align:start position:0%
it's impacted you. Uh you had a lot of
 

00:10:00.160 --> 00:10:01.829 align:start position:0%
it's impacted you. Uh you had a lot of
failed<00:10:00.399><c> businesses</c><00:10:00.880><c> before,</c><00:10:01.360><c> then</c><00:10:01.519><c> you</c><00:10:01.680><c> had</c>

00:10:01.829 --> 00:10:01.839 align:start position:0%
failed businesses before, then you had
 

00:10:01.839 --> 00:10:03.910 align:start position:0%
failed businesses before, then you had
this<00:10:02.000><c> thing</c><00:10:02.160><c> kind</c><00:10:02.399><c> of</c><00:10:02.640><c> take</c><00:10:02.880><c> off.</c><00:10:03.440><c> I'd</c><00:10:03.680><c> love</c><00:10:03.760><c> to</c>

00:10:03.910 --> 00:10:03.920 align:start position:0%
this thing kind of take off. I'd love to
 

00:10:03.920 --> 00:10:05.509 align:start position:0%
this thing kind of take off. I'd love to
hear<00:10:04.080><c> more</c><00:10:04.240><c> about</c><00:10:04.399><c> that</c><00:10:04.720><c> experience</c><00:10:05.040><c> and</c><00:10:05.360><c> how</c>

00:10:05.509 --> 00:10:05.519 align:start position:0%
hear more about that experience and how
 

00:10:05.519 --> 00:10:08.790 align:start position:0%
hear more about that experience and how
it<00:10:05.680><c> changed</c><00:10:06.000><c> things.</c><00:10:06.880><c> SAS</c><00:10:07.440><c> is</c><00:10:07.920><c> incredible.</c><00:10:08.640><c> I</c>

00:10:08.790 --> 00:10:08.800 align:start position:0%
it changed things. SAS is incredible. I
 

00:10:08.800 --> 00:10:10.150 align:start position:0%
it changed things. SAS is incredible. I
think<00:10:08.880><c> that's</c><00:10:09.120><c> kind</c><00:10:09.200><c> of</c><00:10:09.279><c> the</c><00:10:09.440><c> first</c><00:10:09.600><c> thing</c><00:10:09.680><c> I</c><00:10:09.839><c> I</c>

00:10:10.150 --> 00:10:10.160 align:start position:0%
think that's kind of the first thing I I
 

00:10:10.160 --> 00:10:12.389 align:start position:0%
think that's kind of the first thing I I
have<00:10:10.320><c> to</c><00:10:10.399><c> say.</c><00:10:10.640><c> I</c><00:10:10.880><c> went</c><00:10:11.120><c> from</c><00:10:11.279><c> a</c><00:10:11.519><c> servicebased</c>

00:10:12.389 --> 00:10:12.399 align:start position:0%
have to say. I went from a servicebased
 

00:10:12.399 --> 00:10:15.190 align:start position:0%
have to say. I went from a servicebased
business<00:10:12.800><c> into</c><00:10:13.200><c> into</c><00:10:13.519><c> SAS</c><00:10:14.079><c> and</c><00:10:14.640><c> it</c><00:10:14.959><c> just</c>

00:10:15.190 --> 00:10:15.200 align:start position:0%
business into into SAS and it just
 

00:10:15.200 --> 00:10:17.590 align:start position:0%
business into into SAS and it just
allows<00:10:15.519><c> you</c><00:10:15.680><c> to</c><00:10:16.320><c> make</c><00:10:16.720><c> more</c><00:10:17.040><c> money</c><00:10:17.279><c> while</c>

00:10:17.590 --> 00:10:17.600 align:start position:0%
allows you to make more money while
 

00:10:17.600 --> 00:10:19.110 align:start position:0%
allows you to make more money while
getting<00:10:17.760><c> your</c><00:10:17.920><c> time</c><00:10:18.160><c> back.</c><00:10:18.399><c> And</c><00:10:18.560><c> that</c><00:10:18.800><c> that's</c>

00:10:19.110 --> 00:10:19.120 align:start position:0%
getting your time back. And that that's
 

00:10:19.120 --> 00:10:20.870 align:start position:0%
getting your time back. And that that's
huge.<00:10:19.519><c> With</c><00:10:19.760><c> a</c><00:10:19.920><c> properly</c><00:10:20.240><c> running</c><00:10:20.560><c> system,</c>

00:10:20.870 --> 00:10:20.880 align:start position:0%
huge. With a properly running system,
 

00:10:20.880 --> 00:10:22.470 align:start position:0%
huge. With a properly running system,
your<00:10:21.120><c> business</c><00:10:21.360><c> will</c><00:10:21.600><c> make</c><00:10:21.839><c> money</c><00:10:22.079><c> for</c><00:10:22.320><c> you</c>

00:10:22.470 --> 00:10:22.480 align:start position:0%
your business will make money for you
 

00:10:22.480 --> 00:10:23.990 align:start position:0%
your business will make money for you
while<00:10:22.720><c> you</c><00:10:22.880><c> sleep.</c><00:10:23.360><c> That</c><00:10:23.600><c> doesn't</c><00:10:23.760><c> mean</c><00:10:23.839><c> it's</c>

00:10:23.990 --> 00:10:24.000 align:start position:0%
while you sleep. That doesn't mean it's
 

00:10:24.000 --> 00:10:25.590 align:start position:0%
while you sleep. That doesn't mean it's
hands<00:10:24.240><c> off</c><00:10:24.480><c> though</c><00:10:24.720><c> while</c><00:10:24.880><c> you're</c><00:10:25.040><c> awake,</c><00:10:25.360><c> of</c>

00:10:25.590 --> 00:10:25.600 align:start position:0%
hands off though while you're awake, of
 

00:10:25.600 --> 00:10:28.310 align:start position:0%
hands off though while you're awake, of
course,<00:10:25.839><c> but</c><00:10:26.399><c> I</c><00:10:26.560><c> I</c><00:10:26.959><c> was</c><00:10:27.120><c> just</c><00:10:27.279><c> amazed</c><00:10:27.680><c> by</c><00:10:28.160><c> how</c>

00:10:28.310 --> 00:10:28.320 align:start position:0%
course, but I I was just amazed by how
 

00:10:28.320 --> 00:10:29.910 align:start position:0%
course, but I I was just amazed by how
the<00:10:28.560><c> system</c><00:10:28.800><c> can</c><00:10:29.040><c> really</c><00:10:29.200><c> work</c><00:10:29.440><c> when</c><00:10:29.680><c> it</c>

00:10:29.910 --> 00:10:29.920 align:start position:0%
the system can really work when it
 

00:10:29.920 --> 00:10:31.750 align:start position:0%
the system can really work when it
connects.<00:10:30.399><c> Something</c><00:10:30.720><c> else</c><00:10:30.800><c> that</c><00:10:31.040><c> I</c><00:10:31.440><c> realized</c>

00:10:31.750 --> 00:10:31.760 align:start position:0%
connects. Something else that I realized
 

00:10:31.760 --> 00:10:33.110 align:start position:0%
connects. Something else that I realized
a<00:10:31.920><c> little</c><00:10:32.000><c> bit</c><00:10:32.240><c> maybe</c><00:10:32.480><c> more</c><00:10:32.560><c> on</c><00:10:32.720><c> the</c><00:10:32.880><c> personal</c>

00:10:33.110 --> 00:10:33.120 align:start position:0%
a little bit maybe more on the personal
 

00:10:33.120 --> 00:10:34.949 align:start position:0%
a little bit maybe more on the personal
side<00:10:33.279><c> is</c><00:10:33.600><c> it's</c><00:10:33.920><c> important</c><00:10:34.160><c> to</c><00:10:34.320><c> make</c><00:10:34.480><c> sure</c><00:10:34.560><c> that</c>

00:10:34.949 --> 00:10:34.959 align:start position:0%
side is it's important to make sure that
 

00:10:34.959 --> 00:10:37.030 align:start position:0%
side is it's important to make sure that
what<00:10:35.200><c> you're</c><00:10:35.360><c> pursuing</c><00:10:35.839><c> is</c><00:10:36.320><c> for</c><00:10:36.560><c> the</c><00:10:36.720><c> love</c><00:10:36.880><c> of</c>

00:10:37.030 --> 00:10:37.040 align:start position:0%
what you're pursuing is for the love of
 

00:10:37.040 --> 00:10:39.190 align:start position:0%
what you're pursuing is for the love of
doing<00:10:37.200><c> it</c><00:10:37.360><c> because</c><00:10:38.079><c> when</c><00:10:38.320><c> times</c><00:10:38.560><c> get</c><00:10:38.800><c> hard</c><00:10:39.040><c> it</c>

00:10:39.190 --> 00:10:39.200 align:start position:0%
doing it because when times get hard it
 

00:10:39.200 --> 00:10:41.110 align:start position:0%
doing it because when times get hard it
it's<00:10:39.440><c> easy</c><00:10:39.600><c> to</c><00:10:39.760><c> quit.</c><00:10:40.160><c> It</c><00:10:40.320><c> also</c><00:10:40.560><c> gives</c><00:10:40.800><c> us</c><00:10:40.880><c> a</c>

00:10:41.110 --> 00:10:41.120 align:start position:0%
it's easy to quit. It also gives us a
 

00:10:41.120 --> 00:10:42.949 align:start position:0%
it's easy to quit. It also gives us a
competitive<00:10:41.600><c> advantage</c><00:10:42.079><c> because</c><00:10:42.480><c> Jacob</c><00:10:42.800><c> and</c>

00:10:42.949 --> 00:10:42.959 align:start position:0%
competitive advantage because Jacob and
 

00:10:42.959 --> 00:10:44.790 align:start position:0%
competitive advantage because Jacob and
I<00:10:43.120><c> truly</c><00:10:43.440><c> love</c><00:10:43.680><c> doing</c><00:10:43.920><c> this</c><00:10:44.160><c> business</c><00:10:44.560><c> and</c>

00:10:44.790 --> 00:10:44.800 align:start position:0%
I truly love doing this business and
 

00:10:44.800 --> 00:10:46.230 align:start position:0%
I truly love doing this business and
we're<00:10:44.959><c> obsessed</c><00:10:45.279><c> with</c><00:10:45.440><c> it.</c><00:10:45.680><c> We're</c><00:10:45.920><c> always</c>

00:10:46.230 --> 00:10:46.240 align:start position:0%
we're obsessed with it. We're always
 

00:10:46.240 --> 00:10:47.750 align:start position:0%
we're obsessed with it. We're always
thinking<00:10:46.480><c> about</c><00:10:46.640><c> it.</c><00:10:46.880><c> We're</c><00:10:47.120><c> always</c><00:10:47.440><c> texting</c>

00:10:47.750 --> 00:10:47.760 align:start position:0%
thinking about it. We're always texting
 

00:10:47.760 --> 00:10:49.670 align:start position:0%
thinking about it. We're always texting
each<00:10:48.000><c> other</c><00:10:48.079><c> about</c><00:10:48.320><c> the</c><00:10:48.560><c> business</c><00:10:49.120><c> and</c><00:10:49.440><c> if</c>

00:10:49.670 --> 00:10:49.680 align:start position:0%
each other about the business and if
 

00:10:49.680 --> 00:10:51.509 align:start position:0%
each other about the business and if
it's<00:10:49.920><c> not</c><00:10:50.079><c> truly</c><00:10:50.480><c> something</c><00:10:50.720><c> you</c><00:10:50.959><c> love,</c>

00:10:51.509 --> 00:10:51.519 align:start position:0%
it's not truly something you love,
 

00:10:51.519 --> 00:10:53.110 align:start position:0%
it's not truly something you love,
someone<00:10:51.760><c> who</c><00:10:52.000><c> loves</c><00:10:52.320><c> doing</c><00:10:52.560><c> it,</c><00:10:52.800><c> they'll</c>

00:10:53.110 --> 00:10:53.120 align:start position:0%
someone who loves doing it, they'll
 

00:10:53.120 --> 00:10:54.870 align:start position:0%
someone who loves doing it, they'll
always<00:10:53.360><c> surpass</c><00:10:53.760><c> you.</c><00:10:54.399><c> You</c><00:10:54.560><c> have</c><00:10:54.640><c> this</c>

00:10:54.870 --> 00:10:54.880 align:start position:0%
always surpass you. You have this
 

00:10:54.880 --> 00:10:56.870 align:start position:0%
always surpass you. You have this
successful<00:10:55.360><c> business.</c><00:10:55.839><c> You</c><00:10:56.079><c> get</c><00:10:56.240><c> to</c><00:10:56.560><c> sit</c><00:10:56.720><c> down</c>

00:10:56.870 --> 00:10:56.880 align:start position:0%
successful business. You get to sit down
 

00:10:56.880 --> 00:10:57.910 align:start position:0%
successful business. You get to sit down
and<00:10:57.040><c> work</c><00:10:57.120><c> on</c><00:10:57.279><c> it</c><00:10:57.360><c> every</c><00:10:57.519><c> day.</c><00:10:57.680><c> You're</c>

00:10:57.910 --> 00:10:57.920 align:start position:0%
and work on it every day. You're
 

00:10:57.920 --> 00:10:59.509 align:start position:0%
and work on it every day. You're
passionate<00:10:58.240><c> about</c><00:10:58.399><c> it.</c><00:10:58.640><c> What</c><00:10:58.880><c> does</c><00:10:58.959><c> an</c><00:10:59.200><c> actual</c>

00:10:59.509 --> 00:10:59.519 align:start position:0%
passionate about it. What does an actual
 

00:10:59.519 --> 00:11:01.590 align:start position:0%
passionate about it. What does an actual
day<00:10:59.680><c> in</c><00:10:59.839><c> the</c><00:11:00.000><c> life</c><00:11:00.560><c> look</c><00:11:00.800><c> like</c><00:11:01.040><c> for</c><00:11:01.279><c> you?</c><00:11:01.519><c> You</c>

00:11:01.590 --> 00:11:01.600 align:start position:0%
day in the life look like for you? You
 

00:11:01.600 --> 00:11:02.710 align:start position:0%
day in the life look like for you? You
have<00:11:01.680><c> a</c><00:11:01.839><c> million-</c><00:11:02.079><c> dollar</c><00:11:02.240><c> business.</c><00:11:02.640><c> What</c>

00:11:02.710 --> 00:11:02.720 align:start position:0%
have a million- dollar business. What
 

00:11:02.720 --> 00:11:04.389 align:start position:0%
have a million- dollar business. What
does<00:11:02.880><c> that</c><00:11:03.040><c> look</c><00:11:03.200><c> like?</c><00:11:03.600><c> Every</c><00:11:03.839><c> day</c><00:11:04.079><c> it</c><00:11:04.240><c> kind</c>

00:11:04.389 --> 00:11:04.399 align:start position:0%
does that look like? Every day it kind
 

00:11:04.399 --> 00:11:06.470 align:start position:0%
does that look like? Every day it kind
of<00:11:04.480><c> sits</c><00:11:04.640><c> into</c><00:11:04.880><c> two</c><00:11:05.120><c> different</c><00:11:05.279><c> buckets.</c><00:11:06.000><c> I'm</c>

00:11:06.470 --> 00:11:06.480 align:start position:0%
of sits into two different buckets. I'm
 

00:11:06.480 --> 00:11:08.389 align:start position:0%
of sits into two different buckets. I'm
either<00:11:06.880><c> looking</c><00:11:07.120><c> into</c><00:11:07.440><c> ways</c><00:11:07.600><c> to</c><00:11:07.760><c> cut</c><00:11:07.920><c> costs,</c>

00:11:08.389 --> 00:11:08.399 align:start position:0%
either looking into ways to cut costs,
 

00:11:08.399 --> 00:11:10.230 align:start position:0%
either looking into ways to cut costs,
make<00:11:08.560><c> things</c><00:11:08.720><c> more</c><00:11:08.959><c> efficient,</c><00:11:09.680><c> improve</c><00:11:10.000><c> the</c>

00:11:10.230 --> 00:11:10.240 align:start position:0%
make things more efficient, improve the
 

00:11:10.240 --> 00:11:12.470 align:start position:0%
make things more efficient, improve the
product,<00:11:10.880><c> or</c><00:11:11.519><c> bring</c><00:11:11.760><c> on</c><00:11:11.920><c> completely</c><00:11:12.320><c> new</c>

00:11:12.470 --> 00:11:12.480 align:start position:0%
product, or bring on completely new
 

00:11:12.480 --> 00:11:13.750 align:start position:0%
product, or bring on completely new
features,<00:11:12.959><c> integrations,</c><00:11:13.519><c> and</c>

00:11:13.750 --> 00:11:13.760 align:start position:0%
features, integrations, and
 

00:11:13.760 --> 00:11:15.269 align:start position:0%
features, integrations, and
partnerships.<00:11:14.560><c> That's</c><00:11:14.800><c> kind</c><00:11:14.880><c> of</c><00:11:14.959><c> what</c><00:11:15.040><c> a</c><00:11:15.200><c> day</c>

00:11:15.269 --> 00:11:15.279 align:start position:0%
partnerships. That's kind of what a day
 

00:11:15.279 --> 00:11:16.630 align:start position:0%
partnerships. That's kind of what a day
in<00:11:15.440><c> the</c><00:11:15.519><c> life</c><00:11:15.680><c> looks</c><00:11:15.839><c> like</c><00:11:16.000><c> is</c><00:11:16.160><c> just</c><00:11:16.320><c> constant</c>

00:11:16.630 --> 00:11:16.640 align:start position:0%
in the life looks like is just constant
 

00:11:16.640 --> 00:11:18.389 align:start position:0%
in the life looks like is just constant
optimizations.<00:11:17.600><c> I'm</c><00:11:17.839><c> still</c><00:11:18.000><c> doing</c><00:11:18.160><c> bug</c>

00:11:18.389 --> 00:11:18.399 align:start position:0%
optimizations. I'm still doing bug
 

00:11:18.399 --> 00:11:20.150 align:start position:0%
optimizations. I'm still doing bug
reports,<00:11:18.800><c> too.</c><00:11:19.040><c> So,</c><00:11:19.279><c> there's</c><00:11:19.600><c> of</c><00:11:19.760><c> course</c><00:11:19.920><c> the</c>

00:11:20.150 --> 00:11:20.160 align:start position:0%
reports, too. So, there's of course the
 

00:11:20.160 --> 00:11:21.990 align:start position:0%
reports, too. So, there's of course the
mundane<00:11:20.720><c> side</c><00:11:20.880><c> of</c><00:11:20.959><c> things</c><00:11:21.120><c> as</c><00:11:21.360><c> well.</c><00:11:21.680><c> For</c><00:11:21.839><c> me,</c>

00:11:21.990 --> 00:11:22.000 align:start position:0%
mundane side of things as well. For me,
 

00:11:22.000 --> 00:11:24.150 align:start position:0%
mundane side of things as well. For me,
it's<00:11:22.160><c> more</c><00:11:22.320><c> of</c><00:11:22.399><c> a</c><00:11:22.560><c> balancing</c><00:11:22.959><c> act.</c><00:11:23.360><c> It's</c><00:11:23.920><c> just</c>

00:11:24.150 --> 00:11:24.160 align:start position:0%
it's more of a balancing act. It's just
 

00:11:24.160 --> 00:11:26.150 align:start position:0%
it's more of a balancing act. It's just
finding<00:11:24.480><c> new</c><00:11:24.720><c> ways</c><00:11:24.959><c> to</c><00:11:25.200><c> grow</c><00:11:25.519><c> while</c><00:11:25.839><c> keeping</c>

00:11:26.150 --> 00:11:26.160 align:start position:0%
finding new ways to grow while keeping
 

00:11:26.160 --> 00:11:28.069 align:start position:0%
finding new ways to grow while keeping
our<00:11:26.480><c> existing</c><00:11:26.880><c> channels</c><00:11:27.279><c> running</c><00:11:27.519><c> smoothly.</c>

00:11:28.069 --> 00:11:28.079 align:start position:0%
our existing channels running smoothly.
 

00:11:28.079 --> 00:11:30.150 align:start position:0%
our existing channels running smoothly.
So,<00:11:28.480><c> you</c><00:11:28.560><c> know,</c><00:11:28.720><c> creative</c><00:11:29.200><c> strategy,</c><00:11:29.839><c> working</c>

00:11:30.150 --> 00:11:30.160 align:start position:0%
So, you know, creative strategy, working
 

00:11:30.160 --> 00:11:31.990 align:start position:0%
So, you know, creative strategy, working
with<00:11:30.320><c> our</c><00:11:30.640><c> new</c><00:11:30.800><c> team,</c><00:11:31.040><c> as</c><00:11:31.279><c> Jacob</c><00:11:31.680><c> said,</c>

00:11:31.990 --> 00:11:32.000 align:start position:0%
with our new team, as Jacob said,
 

00:11:32.000 --> 00:11:33.990 align:start position:0%
with our new team, as Jacob said,
managing<00:11:32.480><c> partnerships.</c><00:11:33.200><c> So,</c><00:11:33.600><c> it's</c><00:11:33.839><c> just</c>

00:11:33.990 --> 00:11:34.000 align:start position:0%
managing partnerships. So, it's just
 

00:11:34.000 --> 00:11:36.069 align:start position:0%
managing partnerships. So, it's just
figuring<00:11:34.320><c> out</c><00:11:34.480><c> how</c><00:11:34.640><c> to</c><00:11:34.800><c> scale</c><00:11:35.200><c> while</c><00:11:35.519><c> also</c><00:11:35.760><c> not</c>

00:11:36.069 --> 00:11:36.079 align:start position:0%
figuring out how to scale while also not
 

00:11:36.079 --> 00:11:37.910 align:start position:0%
figuring out how to scale while also not
breaking<00:11:36.399><c> what's</c><00:11:36.720><c> already</c><00:11:37.040><c> working.</c><00:11:37.680><c> Thanks</c>

00:11:37.910 --> 00:11:37.920 align:start position:0%
breaking what's already working. Thanks
 

00:11:37.920 --> 00:11:39.269 align:start position:0%
breaking what's already working. Thanks
for<00:11:38.079><c> sharing</c><00:11:38.240><c> that.</c><00:11:38.560><c> Last</c><00:11:38.720><c> question</c><00:11:38.880><c> we</c><00:11:39.040><c> ask</c>

00:11:39.269 --> 00:11:39.279 align:start position:0%
for sharing that. Last question we ask
 

00:11:39.279 --> 00:11:40.630 align:start position:0%
for sharing that. Last question we ask
all<00:11:39.519><c> founders</c><00:11:39.839><c> is</c><00:11:40.079><c> if</c><00:11:40.160><c> you</c><00:11:40.240><c> can</c><00:11:40.320><c> stand</c><00:11:40.480><c> on</c>

00:11:40.630 --> 00:11:40.640 align:start position:0%
all founders is if you can stand on
 

00:11:40.640 --> 00:11:42.150 align:start position:0%
all founders is if you can stand on
Jacob<00:11:40.959><c> and</c><00:11:41.120><c> Alex's</c><00:11:41.519><c> shoulders</c><00:11:41.839><c> when</c><00:11:42.000><c> you're</c>

00:11:42.150 --> 00:11:42.160 align:start position:0%
Jacob and Alex's shoulders when you're
 

00:11:42.160 --> 00:11:43.750 align:start position:0%
Jacob and Alex's shoulders when you're
just<00:11:42.320><c> starting</c><00:11:42.560><c> out</c><00:11:42.800><c> before</c><00:11:43.120><c> the</c><00:11:43.279><c> success,</c>

00:11:43.750 --> 00:11:43.760 align:start position:0%
just starting out before the success,
 

00:11:43.760 --> 00:11:44.870 align:start position:0%
just starting out before the success,
what<00:11:43.839><c> would</c><00:11:44.000><c> you</c><00:11:44.160><c> tell</c><00:11:44.320><c> them?</c><00:11:44.640><c> There's</c>

00:11:44.870 --> 00:11:44.880 align:start position:0%
what would you tell them? There's
 

00:11:44.880 --> 00:11:46.069 align:start position:0%
what would you tell them? There's
there's<00:11:45.120><c> three</c><00:11:45.279><c> things</c><00:11:45.440><c> that</c><00:11:45.600><c> come</c><00:11:45.680><c> to</c><00:11:45.839><c> mind</c>

00:11:46.069 --> 00:11:46.079 align:start position:0%
there's three things that come to mind
 

00:11:46.079 --> 00:11:48.550 align:start position:0%
there's three things that come to mind
for<00:11:46.240><c> me.</c><00:11:46.640><c> The</c><00:11:46.880><c> the</c><00:11:47.200><c> first</c><00:11:47.360><c> is</c><00:11:47.920><c> build</c><00:11:48.160><c> from</c><00:11:48.399><c> day</c>

00:11:48.550 --> 00:11:48.560 align:start position:0%
for me. The the first is build from day
 

00:11:48.560 --> 00:11:50.310 align:start position:0%
for me. The the first is build from day
zero<00:11:48.880><c> optimizing</c><00:11:49.519><c> for</c><00:11:49.760><c> the</c><00:11:49.920><c> best</c><00:11:50.160><c> case</c>

00:11:50.310 --> 00:11:50.320 align:start position:0%
zero optimizing for the best case
 

00:11:50.320 --> 00:11:52.550 align:start position:0%
zero optimizing for the best case
scenario.<00:11:50.880><c> If</c><00:11:51.120><c> a</c><00:11:51.279><c> 100,000</c><00:11:51.839><c> people</c><00:11:52.160><c> signed</c><00:11:52.399><c> up</c>

00:11:52.550 --> 00:11:52.560 align:start position:0%
scenario. If a 100,000 people signed up
 

00:11:52.560 --> 00:11:54.310 align:start position:0%
scenario. If a 100,000 people signed up
today,<00:11:53.120><c> could</c><00:11:53.360><c> your</c><00:11:53.519><c> app</c><00:11:53.680><c> handle</c><00:11:53.920><c> it?</c><00:11:54.160><c> Or</c>

00:11:54.310 --> 00:11:54.320 align:start position:0%
today, could your app handle it? Or
 

00:11:54.320 --> 00:11:55.750 align:start position:0%
today, could your app handle it? Or
would<00:11:54.480><c> you</c><00:11:54.640><c> have</c><00:11:54.800><c> to</c><00:11:54.880><c> rethink</c><00:11:55.200><c> your</c><00:11:55.440><c> pricing</c>

00:11:55.750 --> 00:11:55.760 align:start position:0%
would you have to rethink your pricing
 

00:11:55.760 --> 00:11:57.910 align:start position:0%
would you have to rethink your pricing
model<00:11:56.320><c> and</c><00:11:56.560><c> your</c><00:11:56.800><c> technical</c><00:11:57.279><c> debt</c><00:11:57.519><c> when</c><00:11:57.680><c> that</c>

00:11:57.910 --> 00:11:57.920 align:start position:0%
model and your technical debt when that
 

00:11:57.920 --> 00:12:00.150 align:start position:0%
model and your technical debt when that
starts<00:11:58.160><c> to</c><00:11:58.240><c> happen?</c><00:11:58.800><c> The</c><00:11:59.040><c> other</c><00:11:59.200><c> thing</c><00:11:59.440><c> is</c><00:11:59.920><c> to</c>

00:12:00.150 --> 00:12:00.160 align:start position:0%
starts to happen? The other thing is to
 

00:12:00.160 --> 00:12:01.750 align:start position:0%
starts to happen? The other thing is to
not<00:12:00.320><c> get</c><00:12:00.480><c> too</c><00:12:00.640><c> attached.</c><00:12:01.200><c> If</c><00:12:01.360><c> I</c><00:12:01.519><c> didn't</c><00:12:01.600><c> go</c>

00:12:01.750 --> 00:12:01.760 align:start position:0%
not get too attached. If I didn't go
 

00:12:01.760 --> 00:12:03.670 align:start position:0%
not get too attached. If I didn't go
through<00:12:02.000><c> six</c><00:12:02.160><c> ideas</c><00:12:02.480><c> before</c><00:12:02.800><c> Faceless,</c><00:12:03.440><c> I</c>

00:12:03.670 --> 00:12:03.680 align:start position:0%
through six ideas before Faceless, I
 

00:12:03.680 --> 00:12:04.630 align:start position:0%
through six ideas before Faceless, I
probably<00:12:03.920><c> still</c><00:12:04.079><c> would</c><00:12:04.240><c> have</c><00:12:04.320><c> been</c><00:12:04.480><c> trying</c><00:12:04.560><c> to</c>

00:12:04.630 --> 00:12:04.640 align:start position:0%
probably still would have been trying to
 

00:12:04.640 --> 00:12:07.030 align:start position:0%
probably still would have been trying to
make<00:12:04.800><c> that</c><00:12:04.959><c> first</c><00:12:05.120><c> idea</c><00:12:05.440><c> work,</c><00:12:06.079><c> and</c><00:12:06.560><c> it</c><00:12:06.800><c> just</c>

00:12:07.030 --> 00:12:07.040 align:start position:0%
make that first idea work, and it just
 

00:12:07.040 --> 00:12:08.629 align:start position:0%
make that first idea work, and it just
may<00:12:07.200><c> not</c><00:12:07.360><c> have</c><00:12:07.519><c> worked</c><00:12:07.760><c> at</c><00:12:07.920><c> all.</c><00:12:08.240><c> Maybe</c><00:12:08.399><c> the</c>

00:12:08.629 --> 00:12:08.639 align:start position:0%
may not have worked at all. Maybe the
 

00:12:08.639 --> 00:12:10.230 align:start position:0%
may not have worked at all. Maybe the
idea<00:12:08.880><c> wasn't</c><00:12:09.120><c> good</c><00:12:09.279><c> enough.</c><00:12:09.519><c> Failing</c><00:12:09.920><c> fast</c><00:12:10.079><c> is</c>

00:12:10.230 --> 00:12:10.240 align:start position:0%
idea wasn't good enough. Failing fast is
 

00:12:10.240 --> 00:12:12.629 align:start position:0%
idea wasn't good enough. Failing fast is
a<00:12:10.399><c> win-win.</c><00:12:11.040><c> It</c><00:12:11.279><c> there's</c><00:12:11.600><c> no</c><00:12:12.000><c> lose</c><00:12:12.320><c> scenario</c>

00:12:12.629 --> 00:12:12.639 align:start position:0%
a win-win. It there's no lose scenario
 

00:12:12.639 --> 00:12:14.069 align:start position:0%
a win-win. It there's no lose scenario
with<00:12:12.800><c> that.</c><00:12:13.120><c> The</c><00:12:13.279><c> third</c><00:12:13.440><c> thing</c><00:12:13.519><c> that</c><00:12:13.760><c> comes</c><00:12:13.920><c> to</c>

00:12:14.069 --> 00:12:14.079 align:start position:0%
with that. The third thing that comes to
 

00:12:14.079 --> 00:12:16.230 align:start position:0%
with that. The third thing that comes to
mind<00:12:14.240><c> is</c><00:12:14.399><c> to</c><00:12:14.639><c> stay</c><00:12:14.800><c> bootstrapped</c><00:12:15.519><c> cuz</c><00:12:15.920><c> staying</c>

00:12:16.230 --> 00:12:16.240 align:start position:0%
mind is to stay bootstrapped cuz staying
 

00:12:16.240 --> 00:12:18.230 align:start position:0%
mind is to stay bootstrapped cuz staying
bootstrapped<00:12:16.800><c> forces</c><00:12:17.120><c> you</c><00:12:17.279><c> to</c><00:12:17.440><c> build</c><00:12:17.680><c> lean</c>

00:12:18.230 --> 00:12:18.240 align:start position:0%
bootstrapped forces you to build lean
 

00:12:18.240 --> 00:12:20.230 align:start position:0%
bootstrapped forces you to build lean
and<00:12:18.399><c> and</c><00:12:18.880><c> really</c><00:12:19.120><c> focus</c><00:12:19.440><c> on</c><00:12:19.600><c> your</c><00:12:19.760><c> value.</c><00:12:20.000><c> If</c>

00:12:20.230 --> 00:12:20.240 align:start position:0%
and and really focus on your value. If
 

00:12:20.240 --> 00:12:21.990 align:start position:0%
and and really focus on your value. If
your<00:12:20.399><c> idea</c><00:12:20.560><c> is</c><00:12:20.720><c> valuable</c><00:12:21.040><c> to</c><00:12:21.200><c> others,</c><00:12:21.680><c> then</c>

00:12:21.990 --> 00:12:22.000 align:start position:0%
your idea is valuable to others, then
 

00:12:22.000 --> 00:12:24.069 align:start position:0%
your idea is valuable to others, then
you<00:12:22.240><c> can</c><00:12:22.399><c> scale</c><00:12:22.720><c> healthfully.</c><00:12:23.519><c> And</c><00:12:23.680><c> if</c><00:12:23.839><c> it's</c>

00:12:24.069 --> 00:12:24.079 align:start position:0%
you can scale healthfully. And if it's
 

00:12:24.079 --> 00:12:26.069 align:start position:0%
you can scale healthfully. And if it's
not,<00:12:24.560><c> then</c><00:12:24.880><c> you</c><00:12:25.040><c> can</c><00:12:25.200><c> move</c><00:12:25.360><c> on</c><00:12:25.519><c> and</c><00:12:25.760><c> adapt</c>

00:12:26.069 --> 00:12:26.079 align:start position:0%
not, then you can move on and adapt
 

00:12:26.079 --> 00:12:28.230 align:start position:0%
not, then you can move on and adapt
quickly.<00:12:26.800><c> You</c><00:12:26.959><c> have</c><00:12:27.120><c> options.</c><00:12:27.680><c> So,</c><00:12:27.920><c> I've</c><00:12:28.160><c> been</c>

00:12:28.230 --> 00:12:28.240 align:start position:0%
quickly. You have options. So, I've been
 

00:12:28.240 --> 00:12:29.910 align:start position:0%
quickly. You have options. So, I've been
doing<00:12:28.399><c> this</c><00:12:28.639><c> since</c><00:12:28.880><c> I've</c><00:12:29.120><c> been</c><00:12:29.279><c> young.</c><00:12:29.600><c> I've</c>

00:12:29.910 --> 00:12:29.920 align:start position:0%
doing this since I've been young. I've
 

00:12:29.920 --> 00:12:31.750 align:start position:0%
doing this since I've been young. I've
always<00:12:30.160><c> built</c><00:12:30.399><c> things.</c><00:12:30.880><c> But</c><00:12:31.200><c> don't</c><00:12:31.519><c> watch</c>

00:12:31.750 --> 00:12:31.760 align:start position:0%
always built things. But don't watch
 

00:12:31.760 --> 00:12:33.670 align:start position:0%
always built things. But don't watch
generic<00:12:32.160><c> tutorials.</c><00:12:32.800><c> Just</c><00:12:33.040><c> pick</c><00:12:33.200><c> a</c><00:12:33.440><c> project</c>

00:12:33.670 --> 00:12:33.680 align:start position:0%
generic tutorials. Just pick a project
 

00:12:33.680 --> 00:12:35.670 align:start position:0%
generic tutorials. Just pick a project
that<00:12:34.000><c> you're</c><00:12:34.160><c> genuinely</c><00:12:34.639><c> excited</c><00:12:34.959><c> about</c><00:12:35.440><c> and</c>

00:12:35.670 --> 00:12:35.680 align:start position:0%
that you're genuinely excited about and
 

00:12:35.680 --> 00:12:37.430 align:start position:0%
that you're genuinely excited about and
then<00:12:35.920><c> learn</c><00:12:36.240><c> everything</c><00:12:36.560><c> you</c><00:12:36.800><c> need</c><00:12:37.040><c> to</c><00:12:37.279><c> make</c>

00:12:37.430 --> 00:12:37.440 align:start position:0%
then learn everything you need to make
 

00:12:37.440 --> 00:12:39.509 align:start position:0%
then learn everything you need to make
that<00:12:37.680><c> project</c><00:12:38.079><c> real.</c><00:12:38.720><c> You'll</c><00:12:38.959><c> learn,</c><00:12:39.360><c> you</c>

00:12:39.509 --> 00:12:39.519 align:start position:0%
that project real. You'll learn, you
 

00:12:39.519 --> 00:12:41.269 align:start position:0%
that project real. You'll learn, you
know,<00:12:39.680><c> 10</c><00:12:39.920><c> times</c><00:12:40.240><c> faster</c><00:12:40.639><c> by</c><00:12:40.959><c> building</c>

00:12:41.269 --> 00:12:41.279 align:start position:0%
know, 10 times faster by building
 

00:12:41.279 --> 00:12:42.870 align:start position:0%
know, 10 times faster by building
something<00:12:41.519><c> you</c><00:12:41.760><c> actually</c><00:12:42.079><c> care</c><00:12:42.320><c> about.</c><00:12:42.720><c> And</c>

00:12:42.870 --> 00:12:42.880 align:start position:0%
something you actually care about. And
 

00:12:42.880 --> 00:12:44.389 align:start position:0%
something you actually care about. And
also,<00:12:43.120><c> you</c><00:12:43.279><c> can</c><00:12:43.440><c> leverage</c><00:12:43.760><c> your</c><00:12:44.000><c> existing</c>

00:12:44.389 --> 00:12:44.399 align:start position:0%
also, you can leverage your existing
 

00:12:44.399 --> 00:12:46.470 align:start position:0%
also, you can leverage your existing
skills<00:12:44.720><c> that</c><00:12:44.959><c> you</c><00:12:45.120><c> think</c><00:12:45.360><c> might</c><00:12:45.600><c> not</c><00:12:45.839><c> apply</c><00:12:46.240><c> to</c>

00:12:46.470 --> 00:12:46.480 align:start position:0%
skills that you think might not apply to
 

00:12:46.480 --> 00:12:47.990 align:start position:0%
skills that you think might not apply to
building<00:12:46.720><c> a</c><00:12:46.959><c> business.</c><00:12:47.279><c> I</c><00:12:47.600><c> don't</c><00:12:47.680><c> think</c><00:12:47.760><c> our</c>

00:12:47.990 --> 00:12:48.000 align:start position:0%
building a business. I don't think our
 

00:12:48.000 --> 00:12:50.069 align:start position:0%
building a business. I don't think our
marketing<00:12:48.399><c> would</c><00:12:48.560><c> have</c><00:12:48.720><c> been</c><00:12:48.880><c> as</c><00:12:49.200><c> strong</c><00:12:49.600><c> if</c><00:12:49.839><c> I</c>

00:12:50.069 --> 00:12:50.079 align:start position:0%
marketing would have been as strong if I
 

00:12:50.079 --> 00:12:51.990 align:start position:0%
marketing would have been as strong if I
didn't<00:12:50.320><c> have</c><00:12:50.560><c> a</c><00:12:50.959><c> storytelling</c><00:12:51.519><c> background.</c>

00:12:51.990 --> 00:12:52.000 align:start position:0%
didn't have a storytelling background.
 

00:12:52.000 --> 00:12:53.430 align:start position:0%
didn't have a storytelling background.
And<00:12:52.160><c> I</c><00:12:52.320><c> don't</c><00:12:52.480><c> think</c><00:12:52.560><c> that,</c><00:12:52.959><c> you</c><00:12:53.040><c> know,</c><00:12:53.200><c> a</c><00:12:53.360><c> lot</c>

00:12:53.430 --> 00:12:53.440 align:start position:0%
And I don't think that, you know, a lot
 

00:12:53.440 --> 00:12:55.430 align:start position:0%
And I don't think that, you know, a lot
of<00:12:53.519><c> what</c><00:12:53.760><c> Jacob</c><00:12:54.399><c> did,</c><00:12:54.880><c> you</c><00:12:54.959><c> know,</c><00:12:55.120><c> come</c><00:12:55.279><c> to</c>

00:12:55.430 --> 00:12:55.440 align:start position:0%
of what Jacob did, you know, come to
 

00:12:55.440 --> 00:12:57.670 align:start position:0%
of what Jacob did, you know, come to
fruition<00:12:56.320><c> if</c><00:12:56.639><c> he</c><00:12:56.880><c> didn't</c><00:12:57.120><c> have</c><00:12:57.360><c> his</c>

00:12:57.670 --> 00:12:57.680 align:start position:0%
fruition if he didn't have his
 

00:12:57.680 --> 00:12:59.590 align:start position:0%
fruition if he didn't have his
background<00:12:58.000><c> in</c><00:12:58.240><c> the</c><00:12:58.399><c> music</c><00:12:58.720><c> industry</c><00:12:59.040><c> and</c><00:12:59.360><c> the</c>

00:12:59.590 --> 00:12:59.600 align:start position:0%
background in the music industry and the
 

00:12:59.600 --> 00:13:01.910 align:start position:0%
background in the music industry and the
advertising<00:13:00.079><c> industry.</c><00:13:00.720><c> Thank</c><00:13:00.880><c> you,</c><00:13:01.120><c> Alex.</c>

00:13:01.910 --> 00:13:01.920 align:start position:0%
advertising industry. Thank you, Alex.
 

00:13:01.920 --> 00:13:03.670 align:start position:0%
advertising industry. Thank you, Alex.
Thank<00:13:02.079><c> you,</c><00:13:02.320><c> Jacob,</c><00:13:02.720><c> for</c><00:13:02.959><c> coming</c><00:13:03.120><c> on.</c><00:13:03.519><c> The</c>

00:13:03.670 --> 00:13:03.680 align:start position:0%
Thank you, Jacob, for coming on. The
 

00:13:03.680 --> 00:13:05.590 align:start position:0%
Thank you, Jacob, for coming on. The
business<00:13:03.920><c> you</c><00:13:04.079><c> guys</c><00:13:04.240><c> built</c><00:13:04.480><c> is</c><00:13:04.720><c> amazing.</c><00:13:05.279><c> I'm</c>

00:13:05.590 --> 00:13:05.600 align:start position:0%
business you guys built is amazing. I'm
 

00:13:05.600 --> 00:13:07.110 align:start position:0%
business you guys built is amazing. I'm
sure<00:13:05.680><c> it's</c><00:13:05.920><c> going</c><00:13:06.000><c> to</c><00:13:06.079><c> keep</c><00:13:06.320><c> growing.</c><00:13:06.880><c> The</c>

00:13:07.110 --> 00:13:07.120 align:start position:0%
sure it's going to keep growing. The
 

00:13:07.120 --> 00:13:08.949 align:start position:0%
sure it's going to keep growing. The
fact<00:13:07.279><c> you</c><00:13:07.440><c> built</c><00:13:07.600><c> it</c><00:13:07.760><c> on</c><00:13:07.920><c> Bubble</c><00:13:08.399><c> is</c><00:13:08.639><c> also</c>

00:13:08.949 --> 00:13:08.959 align:start position:0%
fact you built it on Bubble is also
 

00:13:08.959 --> 00:13:10.550 align:start position:0%
fact you built it on Bubble is also
amazing.<00:13:09.440><c> I</c><00:13:09.680><c> haven't</c><00:13:09.839><c> heard</c><00:13:09.920><c> of</c><00:13:10.079><c> that</c><00:13:10.240><c> before,</c>

00:13:10.550 --> 00:13:10.560 align:start position:0%
amazing. I haven't heard of that before,
 

00:13:10.560 --> 00:13:11.990 align:start position:0%
amazing. I haven't heard of that before,
but<00:13:10.720><c> I</c><00:13:10.880><c> think</c><00:13:11.040><c> that's</c><00:13:11.279><c> awesome.</c><00:13:11.680><c> Thanks</c><00:13:11.839><c> for</c>

00:13:11.990 --> 00:13:12.000 align:start position:0%
but I think that's awesome. Thanks for
 

00:13:12.000 --> 00:13:13.829 align:start position:0%
but I think that's awesome. Thanks for
coming<00:13:12.079><c> on</c><00:13:12.320><c> Starter</c><00:13:12.720><c> Story</c><00:13:13.120><c> and</c><00:13:13.440><c> have</c><00:13:13.519><c> a</c><00:13:13.680><c> great</c>

00:13:13.829 --> 00:13:13.839 align:start position:0%
coming on Starter Story and have a great
 

00:13:13.839 --> 00:13:15.910 align:start position:0%
coming on Starter Story and have a great
day.<00:13:14.560><c> Thank</c><00:13:14.720><c> you</c><00:13:14.800><c> so</c><00:13:14.959><c> much</c><00:13:15.040><c> for</c><00:13:15.279><c> having</c><00:13:15.440><c> us.</c>

00:13:15.910 --> 00:13:15.920 align:start position:0%
day. Thank you so much for having us.
 

00:13:15.920 --> 00:13:18.310 align:start position:0%
day. Thank you so much for having us.
Thank<00:13:16.079><c> you.</c><00:13:16.560><c> I</c><00:13:16.800><c> love</c><00:13:17.040><c> Jacob's</c><00:13:17.600><c> story</c><00:13:17.920><c> because</c>

00:13:18.310 --> 00:13:18.320 align:start position:0%
Thank you. I love Jacob's story because
 

00:13:18.320 --> 00:13:20.710 align:start position:0%
Thank you. I love Jacob's story because
he<00:13:18.560><c> went</c><00:13:18.720><c> from</c><00:13:19.040><c> having</c><00:13:19.440><c> basically</c><00:13:20.000><c> no</c><00:13:20.320><c> coding</c>

00:13:20.710 --> 00:13:20.720 align:start position:0%
he went from having basically no coding
 

00:13:20.720 --> 00:13:23.030 align:start position:0%
he went from having basically no coding
experience<00:13:21.279><c> to</c><00:13:21.839><c> building</c><00:13:22.160><c> a</c><00:13:22.480><c> million-dollar</c>

00:13:23.030 --> 00:13:23.040 align:start position:0%
experience to building a million-dollar
 

00:13:23.040 --> 00:13:25.829 align:start position:0%
experience to building a million-dollar
app.<00:13:23.519><c> It's</c><00:13:23.839><c> unreal</c><00:13:24.399><c> to</c><00:13:24.639><c> see</c><00:13:24.959><c> how</c><00:13:25.200><c> these</c><00:13:25.519><c> AI</c>

00:13:25.829 --> 00:13:25.839 align:start position:0%
app. It's unreal to see how these AI
 

00:13:25.839 --> 00:13:27.990 align:start position:0%
app. It's unreal to see how these AI
tools<00:13:26.480><c> are</c><00:13:26.720><c> turning</c><00:13:27.200><c> everyone</c><00:13:27.600><c> into</c><00:13:27.839><c> a</c>

00:13:27.990 --> 00:13:28.000 align:start position:0%
tools are turning everyone into a
 

00:13:28.000 --> 00:13:30.069 align:start position:0%
tools are turning everyone into a
builder<00:13:28.639><c> and</c><00:13:28.959><c> how</c><00:13:29.200><c> they're</c><00:13:29.440><c> getting</c><00:13:29.680><c> better</c>

00:13:30.069 --> 00:13:30.079 align:start position:0%
builder and how they're getting better
 

00:13:30.079 --> 00:13:32.069 align:start position:0%
builder and how they're getting better
every<00:13:30.399><c> single</c><00:13:30.639><c> day.</c><00:13:31.120><c> This</c><00:13:31.360><c> is</c><00:13:31.519><c> why</c><00:13:31.680><c> we've</c><00:13:31.920><c> been</c>

00:13:32.069 --> 00:13:32.079 align:start position:0%
every single day. This is why we've been
 

00:13:32.079 --> 00:13:33.910 align:start position:0%
every single day. This is why we've been
working<00:13:32.240><c> on</c><00:13:32.480><c> something</c><00:13:32.880><c> called</c><00:13:33.440><c> Starter</c>

00:13:33.910 --> 00:13:33.920 align:start position:0%
working on something called Starter
 

00:13:33.920 --> 00:13:36.629 align:start position:0%
working on something called Starter
Story<00:13:34.160><c> Build.</c><00:13:34.800><c> It's</c><00:13:35.360><c> the</c><00:13:35.760><c> place</c><00:13:36.000><c> to</c><00:13:36.240><c> learn</c>

00:13:36.629 --> 00:13:36.639 align:start position:0%
Story Build. It's the place to learn
 

00:13:36.639 --> 00:13:39.190 align:start position:0%
Story Build. It's the place to learn
about<00:13:36.880><c> how</c><00:13:37.120><c> to</c><00:13:37.279><c> build</c><00:13:37.440><c> with</c><00:13:37.680><c> AI</c><00:13:38.399><c> and</c><00:13:38.720><c> how</c><00:13:38.959><c> to</c>

00:13:39.190 --> 00:13:39.200 align:start position:0%
about how to build with AI and how to
 

00:13:39.200 --> 00:13:42.629 align:start position:0%
about how to build with AI and how to
turn<00:13:39.600><c> your</c><00:13:40.000><c> idea</c><00:13:40.480><c> into</c><00:13:40.959><c> a</c><00:13:41.279><c> simple</c><00:13:42.000><c> working</c>

00:13:42.629 --> 00:13:42.639 align:start position:0%
turn your idea into a simple working
 

00:13:42.639 --> 00:13:45.190 align:start position:0%
turn your idea into a simple working
production<00:13:43.200><c> app</c><00:13:43.680><c> that</c><00:13:44.000><c> gets</c><00:13:44.320><c> users</c><00:13:44.800><c> and</c>

00:13:45.190 --> 00:13:45.200 align:start position:0%
production app that gets users and
 

00:13:45.200 --> 00:13:47.269 align:start position:0%
production app that gets users and
potentially<00:13:45.680><c> can</c><00:13:45.839><c> make</c><00:13:46.079><c> money.</c><00:13:46.480><c> In</c><00:13:46.800><c> just</c><00:13:47.040><c> 12</c>

00:13:47.269 --> 00:13:47.279 align:start position:0%
potentially can make money. In just 12
 

00:13:47.279 --> 00:13:49.509 align:start position:0%
potentially can make money. In just 12
days,<00:13:47.839><c> we'll</c><00:13:48.160><c> guide</c><00:13:48.399><c> you</c><00:13:48.800><c> through</c><00:13:48.959><c> the</c><00:13:49.200><c> basics</c>

00:13:49.509 --> 00:13:49.519 align:start position:0%
days, we'll guide you through the basics
 

00:13:49.519 --> 00:13:51.990 align:start position:0%
days, we'll guide you through the basics
of<00:13:49.839><c> building</c><00:13:50.240><c> with</c><00:13:50.480><c> AI</c><00:13:51.200><c> and</c><00:13:51.440><c> you'll</c><00:13:51.680><c> get</c><00:13:51.839><c> the</c>

00:13:51.990 --> 00:13:52.000 align:start position:0%
of building with AI and you'll get the
 

00:13:52.000 --> 00:13:54.389 align:start position:0%
of building with AI and you'll get the
skills<00:13:52.399><c> to</c><00:13:52.639><c> bring</c><00:13:52.880><c> your</c><00:13:53.120><c> ideas</c><00:13:53.440><c> to</c><00:13:53.680><c> life.</c><00:13:54.160><c> If</c>

00:13:54.389 --> 00:13:54.399 align:start position:0%
skills to bring your ideas to life. If
 

00:13:54.399 --> 00:13:55.590 align:start position:0%
skills to bring your ideas to life. If
you're<00:13:54.560><c> interested</c><00:13:54.880><c> in</c><00:13:55.120><c> checking</c><00:13:55.360><c> out</c>

00:13:55.590 --> 00:13:55.600 align:start position:0%
you're interested in checking out
 

00:13:55.600 --> 00:13:57.910 align:start position:0%
you're interested in checking out
Starter<00:13:56.000><c> Story</c><00:13:56.240><c> Build,</c><00:13:56.959><c> check</c><00:13:57.279><c> the</c><00:13:57.519><c> link</c><00:13:57.760><c> in</c>

00:13:57.910 --> 00:13:57.920 align:start position:0%
Starter Story Build, check the link in
 

00:13:57.920 --> 00:13:59.590 align:start position:0%
Starter Story Build, check the link in
the<00:13:58.079><c> description</c><00:13:58.480><c> to</c><00:13:58.720><c> learn</c><00:13:58.959><c> more.</c><00:13:59.279><c> Thank</c><00:13:59.440><c> you</c>

00:13:59.590 --> 00:13:59.600 align:start position:0%
the description to learn more. Thank you
 

00:13:59.600 --> 00:14:01.430 align:start position:0%
the description to learn more. Thank you
guys<00:13:59.839><c> again</c><00:14:00.079><c> for</c><00:14:00.399><c> watching.</c><00:14:00.880><c> I'll</c><00:14:01.120><c> see</c><00:14:01.199><c> you</c><00:14:01.279><c> in</c>

00:14:01.430 --> 00:14:01.440 align:start position:0%
guys again for watching. I'll see you in
 

00:14:01.440 --> 00:14:05.320 align:start position:0%
guys again for watching. I'll see you in
the<00:14:01.519><c> next</c><00:14:01.680><c> one.</c><00:14:02.320><c> Peace.</c>

