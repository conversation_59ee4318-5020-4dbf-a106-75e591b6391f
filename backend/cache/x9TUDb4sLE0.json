{"videoId": "x9TUDb4sLE0", "language": "en", "transcript": [{"id": 1, "start": 0.08, "end": 2.389, "formattedStart": "00:00", "formattedEnd": "00:02", "text": "After six failed SAS ideas, my seventh"}, {"id": 2, "start": 2.399, "end": 5.349, "formattedStart": "00:02", "formattedEnd": "00:05", "text": "made millions. This is <PERSON>. He built a"}, {"id": 3, "start": 5.359, "end": 8.15, "formattedStart": "00:05", "formattedEnd": "00:08", "text": "million-dollar SAS 100% with no code."}, {"id": 4, "start": 8.16, "end": 9.75, "formattedStart": "00:08", "formattedEnd": "00:09", "text": "So, you don't need to know how to"}, {"id": 5, "start": 9.76, "end": 11.11, "formattedStart": "00:09", "formattedEnd": "00:11", "text": "actually code. You don't need to know"}, {"id": 6, "start": 11.12, "end": 13.43, "formattedStart": "00:11", "formattedEnd": "00:13", "text": "code syntax. The tool he built it with"}, {"id": 7, "start": 13.44, "end": 16.55, "formattedStart": "00:13", "formattedEnd": "00:16", "text": "and grew it from 0 to $1 million ARR."}, {"id": 8, "start": 16.56, "end": 20.55, "formattedStart": "00:16", "formattedEnd": "00:20", "text": "It's not lovable. It's not cursor. It's"}, {"id": 9, "start": 20.56, "end": 22.39, "formattedStart": "00:20", "formattedEnd": "00:22", "text": "that's the best part of it is anyone can"}, {"id": 10, "start": 22.4, "end": 23.99, "formattedStart": "00:22", "formattedEnd": "00:23", "text": "get started and understand the basic"}, {"id": 11, "start": 24, "end": 26.63, "formattedStart": "00:24", "formattedEnd": "00:26", "text": "frameworks even of programming. <PERSON> is"}, {"id": 12, "start": 26.64, "end": 28.39, "formattedStart": "00:26", "formattedEnd": "00:28", "text": "proof that you don't need any coding"}, {"id": 13, "start": 28.4, "end": 30.79, "formattedStart": "00:28", "formattedEnd": "00:30", "text": "experience to build a profitable SAS."}, {"id": 14, "start": 30.8, "end": 32.87, "formattedStart": "00:30", "formattedEnd": "00:32", "text": "And not knowing how to code might"}, {"id": 15, "start": 32.88, "end": 34.87, "formattedStart": "00:32", "formattedEnd": "00:34", "text": "actually be a huge advantage. In this"}, {"id": 16, "start": 34.88, "end": 36.79, "formattedStart": "00:34", "formattedEnd": "00:36", "text": "video, <PERSON> and <PERSON> come on to the"}, {"id": 17, "start": 36.8, "end": 38.47, "formattedStart": "00:36", "formattedEnd": "00:38", "text": "channel to break down exactly how they"}, {"id": 18, "start": 38.48, "end": 41.27, "formattedStart": "00:38", "formattedEnd": "00:41", "text": "bootstrapped their SAS to $1 million in"}, {"id": 19, "start": 41.28, "end": 44.31, "formattedStart": "00:41", "formattedEnd": "00:44", "text": "just 10 months. Plus, we'll talk about"}, {"id": 20, "start": 44.32, "end": 46.549, "formattedStart": "00:44", "formattedEnd": "00:46", "text": "the secret to building MVPs with no"}, {"id": 21, "start": 46.559, "end": 48.389, "formattedStart": "00:46", "formattedEnd": "00:48", "text": "code, the marketing strategy that"}, {"id": 22, "start": 48.399, "end": 50.549, "formattedStart": "00:48", "formattedEnd": "00:50", "text": "exploded the business, and how you can"}, {"id": 23, "start": 50.559, "end": 53.029, "formattedStart": "00:50", "formattedEnd": "00:53", "text": "get started building apps without code."}, {"id": 24, "start": 53.039, "end": 56.15, "formattedStart": "00:53", "formattedEnd": "00:56", "text": "Today I'm <PERSON> and this is <PERSON><PERSON>"}, {"id": 25, "start": 56.16, "end": 58.869, "formattedStart": "00:56", "formattedEnd": "00:58", "text": "Story."}, {"id": 26, "start": 58.879, "end": 60.95, "formattedStart": "00:58", "formattedEnd": "01:00", "text": "All right, welcome <PERSON> to <PERSON>er"}, {"id": 27, "start": 60.96, "end": 62.63, "formattedStart": "01:00", "formattedEnd": "01:02", "text": "Story. Tell me a little about the"}, {"id": 28, "start": 62.64, "end": 64.39, "formattedStart": "01:02", "formattedEnd": "01:04", "text": "business that you built and what's your"}, {"id": 29, "start": 64.4, "end": 66.469, "formattedStart": "01:04", "formattedEnd": "01:06", "text": "story. My name is <PERSON> and I built a"}, {"id": 30, "start": 66.479, "end": 68.149, "formattedStart": "01:06", "formattedEnd": "01:08", "text": "million-dollar business with no code."}, {"id": 31, "start": 68.159, "end": 69.67, "formattedStart": "01:08", "formattedEnd": "01:09", "text": "I'm a self-taught developer and the"}, {"id": 32, "start": 69.68, "end": 72.149, "formattedStart": "01:09", "formattedEnd": "01:12", "text": "co-founder of Faceless Video. It's 100%"}, {"id": 33, "start": 72.159, "end": 74.31, "formattedStart": "01:12", "formattedEnd": "01:14", "text": "bootstrapped and I built it from scratch"}, {"id": 34, "start": 74.32, "end": 76.23, "formattedStart": "01:14", "formattedEnd": "01:16", "text": "with <PERSON><PERSON><PERSON> and it's now doing over a"}, {"id": 35, "start": 76.24, "end": 78.39, "formattedStart": "01:16", "formattedEnd": "01:18", "text": "million dollars a year in ARR. Wow,"}, {"id": 36, "start": 78.4, "end": 80.149, "formattedStart": "01:18", "formattedEnd": "01:20", "text": "that's amazing. Uh, can you give me a"}, {"id": 37, "start": 80.15899999999999, "end": 82.39, "formattedStart": "01:20", "formattedEnd": "01:22", "text": "further breakdown of how Face<PERSON> works,"}, {"id": 38, "start": 82.4, "end": 83.67, "formattedStart": "01:22", "formattedEnd": "01:23", "text": "what it does, and some of the numbers"}, {"id": 39, "start": 83.68, "end": 85.35, "formattedStart": "01:23", "formattedEnd": "01:25", "text": "behind the business? It's a SAS that"}, {"id": 40, "start": 85.36, "end": 87.35, "formattedStart": "01:25", "formattedEnd": "01:27", "text": "automates Faceless social media channels"}, {"id": 41, "start": 87.36, "end": 89.429, "formattedStart": "01:27", "formattedEnd": "01:29", "text": "and the user only needs to provide their"}, {"id": 42, "start": 89.439, "end": 91.67, "formattedStart": "01:29", "formattedEnd": "01:31", "text": "channel topic. And our platform writes"}, {"id": 43, "start": 91.68, "end": 93.67, "formattedStart": "01:31", "formattedEnd": "01:33", "text": "the video content, creates the video,"}, {"id": 44, "start": 93.68, "end": 95.35, "formattedStart": "01:33", "formattedEnd": "01:35", "text": "and posts that video on the user's"}, {"id": 45, "start": 95.36, "end": 97.429, "formattedStart": "01:35", "formattedEnd": "01:37", "text": "behalf every single day, entirely on"}, {"id": 46, "start": 97.439, "end": 99.35, "formattedStart": "01:37", "formattedEnd": "01:39", "text": "autopilot. We're generating thousands of"}, {"id": 47, "start": 99.36, "end": 101.749, "formattedStart": "01:39", "formattedEnd": "01:41", "text": "videos every single day for users. Some"}, {"id": 48, "start": 101.759, "end": 103.67, "formattedStart": "01:41", "formattedEnd": "01:43", "text": "of which had well over a million views,"}, {"id": 49, "start": 103.68, "end": 104.95, "formattedStart": "01:43", "formattedEnd": "01:44", "text": "plenty of which have had hundreds of"}, {"id": 50, "start": 104.96000000000001, "end": 106.469, "formattedStart": "01:44", "formattedEnd": "01:46", "text": "thousands of views. Total number of"}, {"id": 51, "start": 106.479, "end": 108.71000000000001, "formattedStart": "01:46", "formattedEnd": "01:48", "text": "users signed up, over 1. 1 million. And"}, {"id": 52, "start": 108.72, "end": 110.63, "formattedStart": "01:48", "formattedEnd": "01:50", "text": "they're all hopping on current and"}, {"id": 53, "start": 110.64, "end": 112.069, "formattedStart": "01:50", "formattedEnd": "01:52", "text": "relevant trends right now. That's an"}, {"id": 54, "start": 112.07900000000001, "end": 114.149, "formattedStart": "01:52", "formattedEnd": "01:54", "text": "amazing business that you built. But I"}, {"id": 55, "start": 114.15899999999999, "end": 116.55, "formattedStart": "01:54", "formattedEnd": "01:56", "text": "want to go back a little bit. Uh what's"}, {"id": 56, "start": 116.56, "end": 118.22999999999999, "formattedStart": "01:56", "formattedEnd": "01:58", "text": "your background and how do you get to"}, {"id": 57, "start": 118.24000000000001, "end": 120.31, "formattedStart": "01:58", "formattedEnd": "02:00", "text": "the point of building a SAS? My"}, {"id": 58, "start": 120.32, "end": 122.789, "formattedStart": "02:00", "formattedEnd": "02:02", "text": "background started as an artist. I uh"}, {"id": 59, "start": 122.799, "end": 125.19, "formattedStart": "02:02", "formattedEnd": "02:05", "text": "started as a songwriter, producer, and I"}, {"id": 60, "start": 125.2, "end": 126.95, "formattedStart": "02:05", "formattedEnd": "02:06", "text": "was releasing music on Spotify, diving"}, {"id": 61, "start": 126.96, "end": 129.35, "formattedStart": "02:06", "formattedEnd": "02:09", "text": "full force into that world. So when I"}, {"id": 62, "start": 129.36, "end": 131.11, "formattedStart": "02:09", "formattedEnd": "02:11", "text": "graduated college, I decided to live at"}, {"id": 63, "start": 131.12, "end": 133.27, "formattedStart": "02:11", "formattedEnd": "02:13", "text": "home with my mom and go full force into"}, {"id": 64, "start": 133.28, "end": 135.67, "formattedStart": "02:13", "formattedEnd": "02:15", "text": "being a songwriter, artist, producer. I"}, {"id": 65, "start": 135.68, "end": 137.589, "formattedStart": "02:15", "formattedEnd": "02:17", "text": "started hiring companies to do marketing"}, {"id": 66, "start": 137.599, "end": 139.67000000000002, "formattedStart": "02:17", "formattedEnd": "02:19", "text": "for me, but I I was just repeatedly"}, {"id": 67, "start": 139.68, "end": 141.03, "formattedStart": "02:19", "formattedEnd": "02:21", "text": "disappointed by all the different"}, {"id": 68, "start": 141.04, "end": 142.63, "formattedStart": "02:21", "formattedEnd": "02:22", "text": "marketing services that I was getting."}, {"id": 69, "start": 142.64, "end": 144.39, "formattedStart": "02:22", "formattedEnd": "02:24", "text": "So, I just figured, okay, I got to go do"}, {"id": 70, "start": 144.4, "end": 146.47, "formattedStart": "02:24", "formattedEnd": "02:26", "text": "this myself. So, I took courses online"}, {"id": 71, "start": 146.48, "end": 149.03, "formattedStart": "02:26", "formattedEnd": "02:29", "text": "about how to market my music and with"}, {"id": 72, "start": 149.04, "end": 150.949, "formattedStart": "02:29", "formattedEnd": "02:30", "text": "very little budget took my music to"}, {"id": 73, "start": 150.959, "end": 153.11, "formattedStart": "02:30", "formattedEnd": "02:33", "text": "close to a million streams and thousands"}, {"id": 74, "start": 153.12, "end": 154.79, "formattedStart": "02:33", "formattedEnd": "02:34", "text": "of genuine fans. That naturally"}, {"id": 75, "start": 154.8, "end": 156.309, "formattedStart": "02:34", "formattedEnd": "02:36", "text": "progressed into helping my friends out"}, {"id": 76, "start": 156.31900000000002, "end": 157.99, "formattedStart": "02:36", "formattedEnd": "02:37", "text": "in the music business and helping them"}, {"id": 77, "start": 158, "end": 159.67000000000002, "formattedStart": "02:38", "formattedEnd": "02:39", "text": "promote their music, which turned into"}, {"id": 78, "start": 159.68, "end": 161.43, "formattedStart": "02:39", "formattedEnd": "02:41", "text": "my first business, Domino. It's a music"}, {"id": 79, "start": 161.44, "end": 163.27, "formattedStart": "02:41", "formattedEnd": "02:43", "text": "marketing agency. And while I love that"}, {"id": 80, "start": 163.28, "end": 165.27, "formattedStart": "02:43", "formattedEnd": "02:45", "text": "experience, what I realized about doing"}, {"id": 81, "start": 165.28, "end": 166.949, "formattedStart": "02:45", "formattedEnd": "02:46", "text": "a service-based business is that my"}, {"id": 82, "start": 166.959, "end": 169.03, "formattedStart": "02:46", "formattedEnd": "02:49", "text": "income was directly correlated with my"}, {"id": 83, "start": 169.04, "end": 170.949, "formattedStart": "02:49", "formattedEnd": "02:50", "text": "time. And I just wanted to scale beyond"}, {"id": 84, "start": 170.959, "end": 173.43, "formattedStart": "02:50", "formattedEnd": "02:53", "text": "my agency and help more people without"}, {"id": 85, "start": 173.44, "end": 174.869, "formattedStart": "02:53", "formattedEnd": "02:54", "text": "sacrificing more of my time. And that"}, {"id": 86, "start": 174.879, "end": 177.50900000000001, "formattedStart": "02:54", "formattedEnd": "02:57", "text": "made me start looking into SAS. So, you"}, {"id": 87, "start": 177.519, "end": 179.82999999999998, "formattedStart": "02:57", "formattedEnd": "02:59", "text": "caught the bug and then you came upon an"}, {"id": 88, "start": 179.84, "end": 182.149, "formattedStart": "02:59", "formattedEnd": "03:02", "text": "idea that kind of changed everything."}, {"id": 89, "start": 182.159, "end": 183.67, "formattedStart": "03:02", "formattedEnd": "03:03", "text": "Let's talk about that idea and I"}, {"id": 90, "start": 183.68, "end": 185.43, "formattedStart": "03:03", "formattedEnd": "03:05", "text": "actually had you bring your co-founder,"}, {"id": 91, "start": 185.44, "end": 187.91, "formattedStart": "03:05", "formattedEnd": "03:07", "text": "<PERSON>, on who was a part of coming up"}, {"id": 92, "start": 187.92, "end": 190.07, "formattedStart": "03:07", "formattedEnd": "03:10", "text": "with that idea. So, <PERSON> is joining us"}, {"id": 93, "start": 190.08, "end": 192.07, "formattedStart": "03:10", "formattedEnd": "03:12", "text": "right now. I'd love to hear from you."}, {"id": 94, "start": 192.08, "end": 193.83, "formattedStart": "03:12", "formattedEnd": "03:13", "text": "Uh, how did you come up with the idea"}, {"id": 95, "start": 193.84, "end": 196.309, "formattedStart": "03:13", "formattedEnd": "03:16", "text": "for Faceless Video? Yeah, so thanks for"}, {"id": 96, "start": 196.319, "end": 198.47, "formattedStart": "03:16", "formattedEnd": "03:18", "text": "having me. I was getting a lot of"}, {"id": 97, "start": 198.48, "end": 201.03, "formattedStart": "03:18", "formattedEnd": "03:21", "text": "Faceless videos on my Tik Tok algorithm."}, {"id": 98, "start": 201.04, "end": 203.50900000000001, "formattedStart": "03:21", "formattedEnd": "03:23", "text": "I had seen a lot of these videos go"}, {"id": 99, "start": 203.519, "end": 206.149, "formattedStart": "03:23", "formattedEnd": "03:26", "text": "extremely viral, and my goal originally"}, {"id": 100, "start": 206.159, "end": 208.149, "formattedStart": "03:26", "formattedEnd": "03:28", "text": "was just to go viral. And I thought to"}, {"id": 101, "start": 208.159, "end": 209.91, "formattedStart": "03:28", "formattedEnd": "03:29", "text": "myself, I can edit these videos. You"}, {"id": 102, "start": 209.92000000000002, "end": 211.19, "formattedStart": "03:29", "formattedEnd": "03:31", "text": "know, they look simple enough. They're"}, {"id": 103, "start": 211.2, "end": 213.43, "formattedStart": "03:31", "formattedEnd": "03:33", "text": "going viral. Why not do it myself? But"}, {"id": 104, "start": 213.44, "end": 215.589, "formattedStart": "03:33", "formattedEnd": "03:35", "text": "then I realized that consistency was a"}, {"id": 105, "start": 215.599, "end": 217.75, "formattedStart": "03:35", "formattedEnd": "03:37", "text": "huge problem. And coming up with new"}, {"id": 106, "start": 217.76, "end": 219.75, "formattedStart": "03:37", "formattedEnd": "03:39", "text": "ideas, editing every day became"}, {"id": 107, "start": 219.76, "end": 221.589, "formattedStart": "03:39", "formattedEnd": "03:41", "text": "something that was actually pretty"}, {"id": 108, "start": 221.599, "end": 224.39, "formattedStart": "03:41", "formattedEnd": "03:44", "text": "difficult to stick to. So, I had an idea"}, {"id": 109, "start": 224.4, "end": 226.309, "formattedStart": "03:44", "formattedEnd": "03:46", "text": "because I realized with, you know, the"}, {"id": 110, "start": 226.31900000000002, "end": 228.07, "formattedStart": "03:46", "formattedEnd": "03:48", "text": "advancements in tech that were going on,"}, {"id": 111, "start": 228.07999999999998, "end": 229.43, "formattedStart": "03:48", "formattedEnd": "03:49", "text": "these videos were simple enough that"}, {"id": 112, "start": 229.44, "end": 231.19, "formattedStart": "03:49", "formattedEnd": "03:51", "text": "this could be done automatically. And"}, {"id": 113, "start": 231.2, "end": 232.71, "formattedStart": "03:51", "formattedEnd": "03:52", "text": "so, I reached out to <PERSON> and then"}, {"id": 114, "start": 232.72, "end": 234.869, "formattedStart": "03:52", "formattedEnd": "03:54", "text": "<PERSON> had the idea to make this into a"}, {"id": 115, "start": 234.879, "end": 237.11, "formattedStart": "03:54", "formattedEnd": "03:57", "text": "SAS product instead of an internal tool"}, {"id": 116, "start": 237.12, "end": 238.71, "formattedStart": "03:57", "formattedEnd": "03:58", "text": "so we could make channels together."}, {"id": 117, "start": 238.72, "end": 240.789, "formattedStart": "03:58", "formattedEnd": "04:00", "text": "Looking back, I'm really glad that I let"}, {"id": 118, "start": 240.799, "end": 243.67, "formattedStart": "04:00", "formattedEnd": "04:03", "text": "the algorithm guide me. And you know, we"}, {"id": 119, "start": 243.68, "end": 245.83, "formattedStart": "04:03", "formattedEnd": "04:05", "text": "all are on Tik Tok and Instagram reels"}, {"id": 120, "start": 245.84, "end": 247.83, "formattedStart": "04:05", "formattedEnd": "04:07", "text": "and we see these trends in videos and"}, {"id": 121, "start": 247.84, "end": 249.99, "formattedStart": "04:07", "formattedEnd": "04:09", "text": "most of the time we scroll past. But if"}, {"id": 122, "start": 250, "end": 252.07, "formattedStart": "04:10", "formattedEnd": "04:12", "text": "you take a second to actually analyze"}, {"id": 123, "start": 252.08, "end": 253.83, "formattedStart": "04:12", "formattedEnd": "04:13", "text": "what's going on, there are plenty of"}, {"id": 124, "start": 253.84, "end": 255.67, "formattedStart": "04:13", "formattedEnd": "04:15", "text": "good business ideas in there. So you"}, {"id": 125, "start": 255.68, "end": 257.59, "formattedStart": "04:15", "formattedEnd": "04:17", "text": "have the idea, now it's time to get"}, {"id": 126, "start": 257.6, "end": 259.67, "formattedStart": "04:17", "formattedEnd": "04:19", "text": "building. Tell me how how did you build"}, {"id": 127, "start": 259.68, "end": 261.99, "formattedStart": "04:19", "formattedEnd": "04:21", "text": "Faceless Video? It started with <PERSON><PERSON><PERSON>"}, {"id": 128, "start": 262, "end": 263.83, "formattedStart": "04:22", "formattedEnd": "04:23", "text": "and APIs. And <PERSON><PERSON><PERSON> was the"}, {"id": 129, "start": 263.84, "end": 265.189, "formattedStart": "04:23", "formattedEnd": "04:25", "text": "infrastructure for bringing everything"}, {"id": 130, "start": 265.199, "end": 267.189, "formattedStart": "04:25", "formattedEnd": "04:27", "text": "together. And like with anything else,"}, {"id": 131, "start": 267.199, "end": 269.11, "formattedStart": "04:27", "formattedEnd": "04:29", "text": "it always starts with can this work from"}, {"id": 132, "start": 269.12, "end": 270.71, "formattedStart": "04:29", "formattedEnd": "04:30", "text": "a technical perspective at least. You"}, {"id": 133, "start": 270.72, "end": 272.71, "formattedStart": "04:30", "formattedEnd": "04:32", "text": "know, can I actually find a way to type"}, {"id": 134, "start": 272.72, "end": 275.59000000000003, "formattedStart": "04:32", "formattedEnd": "04:35", "text": "in text and have this web app output a"}, {"id": 135, "start": 275.6, "end": 277.35, "formattedStart": "04:35", "formattedEnd": "04:37", "text": "faceless video. So for the first month,"}, {"id": 136, "start": 277.36, "end": 279.03, "formattedStart": "04:37", "formattedEnd": "04:39", "text": "that's really all this MVP was. And that"}, {"id": 137, "start": 279.04, "end": 281.35, "formattedStart": "04:39", "formattedEnd": "04:41", "text": "eventually migrated into managing users,"}, {"id": 138, "start": 281.36, "end": 283.83, "formattedStart": "04:41", "formattedEnd": "04:43", "text": "having a UIUX flow, a payment processing"}, {"id": 139, "start": 283.84000000000003, "end": 286.31, "formattedStart": "04:43", "formattedEnd": "04:46", "text": "system, a pricing model, and turning it"}, {"id": 140, "start": 286.32, "end": 288.31, "formattedStart": "04:46", "formattedEnd": "04:48", "text": "into a full-fledged web app. Okay. So"}, {"id": 141, "start": 288.32, "end": 289.99, "formattedStart": "04:48", "formattedEnd": "04:49", "text": "tell me why <PERSON><PERSON><PERSON> is great for"}, {"id": 142, "start": 290, "end": 292.79, "formattedStart": "04:50", "formattedEnd": "04:52", "text": "non-developers. In its simplest form, it"}, {"id": 143, "start": 292.8, "end": 294.55, "formattedStart": "04:52", "formattedEnd": "04:54", "text": "translates programming into human"}, {"id": 144, "start": 294.56, "end": 296.71, "formattedStart": "04:54", "formattedEnd": "04:56", "text": "language. So you don't need to know how"}, {"id": 145, "start": 296.72, "end": 298.31, "formattedStart": "04:56", "formattedEnd": "04:58", "text": "to actually code. You don't need to know"}, {"id": 146, "start": 298.32, "end": 301.35, "formattedStart": "04:58", "formattedEnd": "05:01", "text": "code syntax is plain English. And that's"}, {"id": 147, "start": 301.36, "end": 303.03, "formattedStart": "05:01", "formattedEnd": "05:03", "text": "the best part of it is anyone can get"}, {"id": 148, "start": 303.04, "end": 304.55, "formattedStart": "05:03", "formattedEnd": "05:04", "text": "started and understand the basic"}, {"id": 149, "start": 304.56, "end": 306.79, "formattedStart": "05:04", "formattedEnd": "05:06", "text": "frameworks even of programming. Because"}, {"id": 150, "start": 306.8, "end": 308.31, "formattedStart": "05:06", "formattedEnd": "05:08", "text": "now that I actually know how to code, I"}, {"id": 151, "start": 308.32, "end": 310.39, "formattedStart": "05:08", "formattedEnd": "05:10", "text": "see that it is actually still very much"}, {"id": 152, "start": 310.4, "end": 312.71, "formattedStart": "05:10", "formattedEnd": "05:12", "text": "programming just in a more visual human"}, {"id": 153, "start": 312.72, "end": 314.55, "formattedStart": "05:12", "formattedEnd": "05:14", "text": "friendly way. And they also handle"}, {"id": 154, "start": 314.56, "end": 316.23, "formattedStart": "05:14", "formattedEnd": "05:16", "text": "plenty of other important aspects of"}, {"id": 155, "start": 316.24, "end": 317.909, "formattedStart": "05:16", "formattedEnd": "05:17", "text": "actually running a web app such as"}, {"id": 156, "start": 317.919, "end": 320.87, "formattedStart": "05:17", "formattedEnd": "05:20", "text": "security, scalability, privacy rules,"}, {"id": 157, "start": 320.88, "end": 322.79, "formattedStart": "05:20", "formattedEnd": "05:22", "text": "backend management, all these other"}, {"id": 158, "start": 322.8, "end": 325.029, "formattedStart": "05:22", "formattedEnd": "05:25", "text": "things that I would not have even known"}, {"id": 159, "start": 325.039, "end": 326.95, "formattedStart": "05:25", "formattedEnd": "05:26", "text": "where to begin if I was just coming at"}, {"id": 160, "start": 326.96, "end": 328.95, "formattedStart": "05:26", "formattedEnd": "05:28", "text": "it completely fresh or custom. You know,"}, {"id": 161, "start": 328.96, "end": 330.31, "formattedStart": "05:28", "formattedEnd": "05:30", "text": "I talked to a lot of people who will"}, {"id": 162, "start": 330.32, "end": 332.469, "formattedStart": "05:30", "formattedEnd": "05:32", "text": "start their app on Bubble, but then, you"}, {"id": 163, "start": 332.479, "end": 334.469, "formattedStart": "05:32", "formattedEnd": "05:34", "text": "know, once things get too complicated"}, {"id": 164, "start": 334.479, "end": 335.99, "formattedStart": "05:34", "formattedEnd": "05:35", "text": "and they really want to scale, they have"}, {"id": 165, "start": 336, "end": 337.43, "formattedStart": "05:36", "formattedEnd": "05:37", "text": "to move off it, which ends up being a"}, {"id": 166, "start": 337.44, "end": 339.909, "formattedStart": "05:37", "formattedEnd": "05:39", "text": "huge thing. But as I understand, you"}, {"id": 167, "start": 339.919, "end": 341.35, "formattedStart": "05:39", "formattedEnd": "05:41", "text": "your app, which is making a million"}, {"id": 168, "start": 341.36, "end": 343.11, "formattedStart": "05:41", "formattedEnd": "05:43", "text": "dollars a year right now, is still on"}, {"id": 169, "start": 343.12, "end": 345.749, "formattedStart": "05:43", "formattedEnd": "05:45", "text": "Bubble. Why have you decided to stay on"}, {"id": 170, "start": 345.759, "end": 347.83, "formattedStart": "05:45", "formattedEnd": "05:47", "text": "Bubble? If it ain't broke, don't fix it."}, {"id": 171, "start": 347.84000000000003, "end": 349.189, "formattedStart": "05:47", "formattedEnd": "05:49", "text": "It's kind of as simple as that. You"}, {"id": 172, "start": 349.199, "end": 350.469, "formattedStart": "05:49", "formattedEnd": "05:50", "text": "know, we haven't run into any issues"}, {"id": 173, "start": 350.479, "end": 353.11, "formattedStart": "05:50", "formattedEnd": "05:53", "text": "that would require a dramatic shift. I I"}, {"id": 174, "start": 353.12, "end": 355.99, "formattedStart": "05:53", "formattedEnd": "05:55", "text": "ran into a scalability issue once, but"}, {"id": 175, "start": 356, "end": 358.23, "formattedStart": "05:56", "formattedEnd": "05:58", "text": "Bubble support fixed it, and that was"}, {"id": 176, "start": 358.24, "end": 359.51, "formattedStart": "05:58", "formattedEnd": "05:59", "text": "really good piece of mind for staying"}, {"id": 177, "start": 359.52, "end": 361.27, "formattedStart": "05:59", "formattedEnd": "06:01", "text": "longterm with them. And realistically,"}, {"id": 178, "start": 361.28, "end": 362.39, "formattedStart": "06:01", "formattedEnd": "06:02", "text": "all the bells and whistles that"}, {"id": 179, "start": 362.4, "end": 364.309, "formattedStart": "06:02", "formattedEnd": "06:04", "text": "developers get through custom code is"}, {"id": 180, "start": 364.319, "end": 365.99, "formattedStart": "06:04", "formattedEnd": "06:05", "text": "not necessary for building a successful"}, {"id": 181, "start": 366, "end": 367.909, "formattedStart": "06:06", "formattedEnd": "06:07", "text": "business. All you need is a great idea,"}, {"id": 182, "start": 367.919, "end": 370.07, "formattedStart": "06:07", "formattedEnd": "06:10", "text": "a functioning product, and a great go to"}, {"id": 183, "start": 370.08, "end": 371.749, "formattedStart": "06:10", "formattedEnd": "06:11", "text": "market strategy, and you can validate"}, {"id": 184, "start": 371.759, "end": 374.07, "formattedStart": "06:11", "formattedEnd": "06:14", "text": "really quickly and scale based on that."}, {"id": 185, "start": 374.08, "end": 375.51, "formattedStart": "06:14", "formattedEnd": "06:15", "text": "All right, let's pause for a quick"}, {"id": 186, "start": 375.52, "end": 377.83, "formattedStart": "06:15", "formattedEnd": "06:17", "text": "moment to talk about AI. Everyone's"}, {"id": 187, "start": 377.84, "end": 380.23, "formattedStart": "06:17", "formattedEnd": "06:20", "text": "hyped up right now about using AI to"}, {"id": 188, "start": 380.24, "end": 382.39, "formattedStart": "06:20", "formattedEnd": "06:22", "text": "build apps. But right now, I'm seeing"}, {"id": 189, "start": 382.4, "end": 384.469, "formattedStart": "06:22", "formattedEnd": "06:24", "text": "that most people get stuck in what I"}, {"id": 190, "start": 384.479, "end": 387.029, "formattedStart": "06:24", "formattedEnd": "06:27", "text": "call a death spiral of prompting. They"}, {"id": 191, "start": 387.039, "end": 388.87, "formattedStart": "06:27", "formattedEnd": "06:28", "text": "have lots of chats with the AI, but the"}, {"id": 192, "start": 388.88, "end": 391.43, "formattedStart": "06:28", "formattedEnd": "06:31", "text": "code never actually works. And that's"}, {"id": 193, "start": 391.44, "end": 393.27, "formattedStart": "06:31", "formattedEnd": "06:33", "text": "exactly why we're partnering with"}, {"id": 194, "start": 393.28, "end": 395.43, "formattedStart": "06:33", "formattedEnd": "06:35", "text": "Bubble. Bubble AI doesn't put your idea"}, {"id": 195, "start": 395.44, "end": 397.43, "formattedStart": "06:35", "formattedEnd": "06:37", "text": "into a cookie cutter template. It"}, {"id": 196, "start": 397.44, "end": 399.909, "formattedStart": "06:37", "formattedEnd": "06:39", "text": "actually blows it up into a full stack"}, {"id": 197, "start": 399.919, "end": 401.749, "formattedStart": "06:39", "formattedEnd": "06:41", "text": "working app. All you have to do is"}, {"id": 198, "start": 401.759, "end": 403.99, "formattedStart": "06:41", "formattedEnd": "06:43", "text": "describe your idea and Bubble AI will"}, {"id": 199, "start": 404, "end": 406.71, "formattedStart": "06:44", "formattedEnd": "06:46", "text": "spin up the entire stack in minutes. A"}, {"id": 200, "start": 406.72, "end": 410.30899999999997, "formattedStart": "06:46", "formattedEnd": "06:50", "text": "polished UI, a pre-wired database, and"}, {"id": 201, "start": 410.319, "end": 412.55, "formattedStart": "06:50", "formattedEnd": "06:52", "text": "workflows that are ready to roll. I've"}, {"id": 202, "start": 412.56, "end": 413.909, "formattedStart": "06:52", "formattedEnd": "06:53", "text": "been poking around with it to build some"}, {"id": 203, "start": 413.919, "end": 415.749, "formattedStart": "06:53", "formattedEnd": "06:55", "text": "stuff for Starter Story. And watching it"}, {"id": 204, "start": 415.759, "end": 417.51, "formattedStart": "06:55", "formattedEnd": "06:57", "text": "just build everything while I sit there"}, {"id": 205, "start": 417.52, "end": 419.749, "formattedStart": "06:57", "formattedEnd": "06:59", "text": "and enjoy my drink is pretty amazing."}, {"id": 206, "start": 419.759, "end": 421.749, "formattedStart": "06:59", "formattedEnd": "07:01", "text": "So, if you've been sitting on an idea,"}, {"id": 207, "start": 421.759, "end": 422.87, "formattedStart": "07:01", "formattedEnd": "07:02", "text": "head to the first link in the"}, {"id": 208, "start": 422.88, "end": 425.749, "formattedStart": "07:02", "formattedEnd": "07:05", "text": "description, sign up for free, and tell"}, {"id": 209, "start": 425.759, "end": 427.749, "formattedStart": "07:05", "formattedEnd": "07:07", "text": "Bubble AI exactly what you want to"}, {"id": 210, "start": 427.759, "end": 430.07, "formattedStart": "07:07", "formattedEnd": "07:10", "text": "build. If you do that, drop your idea in"}, {"id": 211, "start": 430.08, "end": 431.589, "formattedStart": "07:10", "formattedEnd": "07:11", "text": "the comments. I'll read through them and"}, {"id": 212, "start": 431.599, "end": 432.95, "formattedStart": "07:11", "formattedEnd": "07:12", "text": "let you know which ones are my favorite."}, {"id": 213, "start": 432.96, "end": 434.87, "formattedStart": "07:12", "formattedEnd": "07:14", "text": "All right, let's dive back into <PERSON>"}, {"id": 214, "start": 434.88, "end": 437.35, "formattedStart": "07:14", "formattedEnd": "07:17", "text": "and <PERSON> story. On that note, um, what"}, {"id": 215, "start": 437.36, "end": 438.95, "formattedStart": "07:17", "formattedEnd": "07:18", "text": "would be some of the opportunities that"}, {"id": 216, "start": 438.96, "end": 441.67, "formattedStart": "07:18", "formattedEnd": "07:21", "text": "you see right now for ideas that people"}, {"id": 217, "start": 441.68, "end": 443.99, "formattedStart": "07:21", "formattedEnd": "07:23", "text": "could build with Bubble or other no code"}, {"id": 218, "start": 444, "end": 445.909, "formattedStart": "07:24", "formattedEnd": "07:25", "text": "tools? I think marketplaces are kind of"}, {"id": 219, "start": 445.919, "end": 446.95, "formattedStart": "07:25", "formattedEnd": "07:26", "text": "the first thing that comes to mind"}, {"id": 220, "start": 446.96, "end": 449.51, "formattedStart": "07:26", "formattedEnd": "07:29", "text": "actually. Just because it's the easiest"}, {"id": 221, "start": 449.52, "end": 451.189, "formattedStart": "07:29", "formattedEnd": "07:31", "text": "way to manage a front end and a backend"}, {"id": 222, "start": 451.199, "end": 452.629, "formattedStart": "07:31", "formattedEnd": "07:32", "text": "through no code. It's a little bit more"}, {"id": 223, "start": 452.639, "end": 454.469, "formattedStart": "07:32", "formattedEnd": "07:34", "text": "generic, but honestly, you can build"}, {"id": 224, "start": 454.479, "end": 456.07, "formattedStart": "07:34", "formattedEnd": "07:36", "text": "anything if you get creative. But even"}, {"id": 225, "start": 456.08, "end": 457.35, "formattedStart": "07:36", "formattedEnd": "07:37", "text": "if it's no code, that doesn't"}, {"id": 226, "start": 457.36, "end": 459.189, "formattedStart": "07:37", "formattedEnd": "07:39", "text": "necessarily mean that it's easy. Uh you"}, {"id": 227, "start": 459.199, "end": 460.469, "formattedStart": "07:39", "formattedEnd": "07:40", "text": "still have to get creative. You still"}, {"id": 228, "start": 460.479, "end": 463.11, "formattedStart": "07:40", "formattedEnd": "07:43", "text": "need to be resourceful, but there's"}, {"id": 229, "start": 463.12, "end": 464.55, "formattedStart": "07:43", "formattedEnd": "07:44", "text": "plenty of opportunities with no code."}, {"id": 230, "start": 464.56, "end": 466.469, "formattedStart": "07:44", "formattedEnd": "07:46", "text": "And it brings down the barrier to entry"}, {"id": 231, "start": 466.479, "end": 468.95, "formattedStart": "07:46", "formattedEnd": "07:48", "text": "enough so that you can start. And"}, {"id": 232, "start": 468.96, "end": 471.35, "formattedStart": "07:48", "formattedEnd": "07:51", "text": "starting is the biggest hurdle. Well,"}, {"id": 233, "start": 471.36, "end": 473.11, "formattedStart": "07:51", "formattedEnd": "07:53", "text": "that's awesome. So, I want to switch"}, {"id": 234, "start": 473.12, "end": 475.99, "formattedStart": "07:53", "formattedEnd": "07:55", "text": "gears to marketing distribution. How did"}, {"id": 235, "start": 476, "end": 479.11, "formattedStart": "07:56", "formattedEnd": "07:59", "text": "you take this idea from zero to a"}, {"id": 236, "start": 479.12, "end": 481.43, "formattedStart": "07:59", "formattedEnd": "08:01", "text": "million dollars a year? To have success"}, {"id": 237, "start": 481.44, "end": 484.55, "formattedStart": "08:01", "formattedEnd": "08:04", "text": "as a B2C app, you need to have viral"}, {"id": 238, "start": 484.56, "end": 486.71, "formattedStart": "08:04", "formattedEnd": "08:06", "text": "potential. Your CAC, customer"}, {"id": 239, "start": 486.72, "end": 489.029, "formattedStart": "08:06", "formattedEnd": "08:09", "text": "acquisition costs, it's pretty low. And"}, {"id": 240, "start": 489.039, "end": 491.43, "formattedStart": "08:09", "formattedEnd": "08:11", "text": "so, to spend a lot on ads or other"}, {"id": 241, "start": 491.44, "end": 493.749, "formattedStart": "08:11", "formattedEnd": "08:13", "text": "methods of promotion is not really"}, {"id": 242, "start": 493.759, "end": 496.15, "formattedStart": "08:13", "formattedEnd": "08:16", "text": "viable for a a B2C app. That's something"}, {"id": 243, "start": 496.16, "end": 498.15, "formattedStart": "08:16", "formattedEnd": "08:18", "text": "I learned with Faces that my other"}, {"id": 244, "start": 498.16, "end": 499.749, "formattedStart": "08:18", "formattedEnd": "08:19", "text": "products really didn't have. We didn't"}, {"id": 245, "start": 499.759, "end": 501.51, "formattedStart": "08:19", "formattedEnd": "08:21", "text": "reinvent the wheel. We do all of the"}, {"id": 246, "start": 501.52, "end": 503.589, "formattedStart": "08:21", "formattedEnd": "08:23", "text": "usual stuff, you know, we do SEO, we do"}, {"id": 247, "start": 503.599, "end": 506.15, "formattedStart": "08:23", "formattedEnd": "08:26", "text": "ads, influencers, organic content, but"}, {"id": 248, "start": 506.16, "end": 508.07, "formattedStart": "08:26", "formattedEnd": "08:28", "text": "the key is really getting your messaging"}, {"id": 249, "start": 508.08, "end": 510.39, "formattedStart": "08:28", "formattedEnd": "08:30", "text": "right and it's nothing fancy. It's just"}, {"id": 250, "start": 510.4, "end": 513.029, "formattedStart": "08:30", "formattedEnd": "08:33", "text": "good execution. So, I come from a film"}, {"id": 251, "start": 513.039, "end": 515.19, "formattedStart": "08:33", "formattedEnd": "08:35", "text": "making background and that's a lot about"}, {"id": 252, "start": 515.2, "end": 516.949, "formattedStart": "08:35", "formattedEnd": "08:36", "text": "storytelling. So, if you can crack"}, {"id": 253, "start": 516.9590000000001, "end": 519.029, "formattedStart": "08:36", "formattedEnd": "08:39", "text": "storytelling, then you can really sell a"}, {"id": 254, "start": 519.039, "end": 520.7090000000001, "formattedStart": "08:39", "formattedEnd": "08:40", "text": "product. What you want to do is"}, {"id": 255, "start": 520.719, "end": 523.5889999999999, "formattedStart": "08:40", "formattedEnd": "08:43", "text": "kickstart telling that story with ads,"}, {"id": 256, "start": 523.599, "end": 525.269, "formattedStart": "08:43", "formattedEnd": "08:45", "text": "influencer collaborations, and other"}, {"id": 257, "start": 525.279, "end": 527.269, "formattedStart": "08:45", "formattedEnd": "08:47", "text": "things. That's what <PERSON> and <PERSON> did. Our"}, {"id": 258, "start": 527.279, "end": 529.19, "formattedStart": "08:47", "formattedEnd": "08:49", "text": "first advertisement for Faceless Video"}, {"id": 259, "start": 529.2, "end": 531.19, "formattedStart": "08:49", "formattedEnd": "08:51", "text": "was a Twitter thread that we spent I"}, {"id": 260, "start": 531.2, "end": 533.35, "formattedStart": "08:51", "formattedEnd": "08:53", "text": "don't know like 200 bucks on and we got"}, {"id": 261, "start": 533.36, "end": 535.35, "formattedStart": "08:53", "formattedEnd": "08:55", "text": "hundreds of thousands of views and kind"}, {"id": 262, "start": 535.36, "end": 537.829, "formattedStart": "08:55", "formattedEnd": "08:57", "text": "of instantly went viral. Um, and that"}, {"id": 263, "start": 537.8389999999999, "end": 539.829, "formattedStart": "08:57", "formattedEnd": "08:59", "text": "was very very helpful for us. And also"}, {"id": 264, "start": 539.8389999999999, "end": 541.91, "formattedStart": "08:59", "formattedEnd": "09:01", "text": "our biggest growth came when influencers"}, {"id": 265, "start": 541.92, "end": 543.75, "formattedStart": "09:01", "formattedEnd": "09:03", "text": "who saw our ads that we'd never even"}, {"id": 266, "start": 543.76, "end": 545.509, "formattedStart": "09:03", "formattedEnd": "09:05", "text": "reached out to started promoting"}, {"id": 267, "start": 545.519, "end": 547.829, "formattedStart": "09:05", "formattedEnd": "09:07", "text": "Faceless. Video on their own because they"}, {"id": 268, "start": 547.839, "end": 549.35, "formattedStart": "09:07", "formattedEnd": "09:09", "text": "genuinely believe that their audience"}, {"id": 269, "start": 549.36, "end": 551.75, "formattedStart": "09:09", "formattedEnd": "09:11", "text": "would like the product. And <PERSON> also"}, {"id": 270, "start": 551.76, "end": 553.269, "formattedStart": "09:11", "formattedEnd": "09:13", "text": "knows this, but word of mouth is"}, {"id": 271, "start": 553.279, "end": 555.11, "formattedStart": "09:13", "formattedEnd": "09:15", "text": "consistently one of our top five"}, {"id": 272, "start": 555.12, "end": 557.43, "formattedStart": "09:15", "formattedEnd": "09:17", "text": "attribution sources. So like on our"}, {"id": 273, "start": 557.44, "end": 558.87, "formattedStart": "09:17", "formattedEnd": "09:18", "text": "customer attribution thing, people will"}, {"id": 274, "start": 558.88, "end": 560.79, "formattedStart": "09:18", "formattedEnd": "09:20", "text": "just type in friend. So you know, people"}, {"id": 275, "start": 560.8, "end": 562.55, "formattedStart": "09:20", "formattedEnd": "09:22", "text": "actually like the product. But if you"}, {"id": 276, "start": 562.56, "end": 564.31, "formattedStart": "09:22", "formattedEnd": "09:24", "text": "have something that's truly original and"}, {"id": 277, "start": 564.32, "end": 566.31, "formattedStart": "09:24", "formattedEnd": "09:26", "text": "solves a pain point, especially around"}, {"id": 278, "start": 566.32, "end": 568.71, "formattedStart": "09:26", "formattedEnd": "09:28", "text": "an emerging trend, people will notice if"}, {"id": 279, "start": 568.72, "end": 570.389, "formattedStart": "09:28", "formattedEnd": "09:30", "text": "you're first to market with something."}, {"id": 280, "start": 570.399, "end": 572.79, "formattedStart": "09:30", "formattedEnd": "09:32", "text": "So that's what we kind of centered our"}, {"id": 281, "start": 572.8, "end": 574.79, "formattedStart": "09:32", "formattedEnd": "09:34", "text": "whole marketing strategy around is"}, {"id": 282, "start": 574.8, "end": 577.269, "formattedStart": "09:34", "formattedEnd": "09:37", "text": "showing how are we different from what's"}, {"id": 283, "start": 577.279, "end": 579.91, "formattedStart": "09:37", "formattedEnd": "09:39", "text": "out there? And then also this is how you"}, {"id": 284, "start": 579.92, "end": 581.829, "formattedStart": "09:39", "formattedEnd": "09:41", "text": "can tap into this existing trend in a"}, {"id": 285, "start": 581.8389999999999, "end": 583.91, "formattedStart": "09:41", "formattedEnd": "09:43", "text": "new way. So you can lay the foundation"}, {"id": 286, "start": 583.92, "end": 585.91, "formattedStart": "09:43", "formattedEnd": "09:45", "text": "with traditional marketing like ads,"}, {"id": 287, "start": 585.92, "end": 587.75, "formattedStart": "09:45", "formattedEnd": "09:47", "text": "influencers and all these things. But"}, {"id": 288, "start": 587.76, "end": 589.509, "formattedStart": "09:47", "formattedEnd": "09:49", "text": "virality happens when the product kind"}, {"id": 289, "start": 589.519, "end": 591.03, "formattedStart": "09:49", "formattedEnd": "09:51", "text": "of speaks for itself and you have the"}, {"id": 290, "start": 591.04, "end": 592.55, "formattedStart": "09:51", "formattedEnd": "09:52", "text": "proper messaging and all of the"}, {"id": 291, "start": 592.56, "end": 594.07, "formattedStart": "09:52", "formattedEnd": "09:54", "text": "incentives are lined up. I want to"}, {"id": 292, "start": 594.08, "end": 596.23, "formattedStart": "09:54", "formattedEnd": "09:56", "text": "switch gears a little bit and talk about"}, {"id": 293, "start": 596.24, "end": 598.15, "formattedStart": "09:56", "formattedEnd": "09:58", "text": "uh this business that you built and how"}, {"id": 294, "start": 598.16, "end": 600.15, "formattedStart": "09:58", "formattedEnd": "10:00", "text": "it's impacted you. Uh you had a lot of"}, {"id": 295, "start": 600.16, "end": 601.829, "formattedStart": "10:00", "formattedEnd": "10:01", "text": "failed businesses before, then you had"}, {"id": 296, "start": 601.839, "end": 603.91, "formattedStart": "10:01", "formattedEnd": "10:03", "text": "this thing kind of take off. I'd love to"}, {"id": 297, "start": 603.92, "end": 605.509, "formattedStart": "10:03", "formattedEnd": "10:05", "text": "hear more about that experience and how"}, {"id": 298, "start": 605.519, "end": 608.79, "formattedStart": "10:05", "formattedEnd": "10:08", "text": "it changed things. SAS is incredible. I"}, {"id": 299, "start": 608.8, "end": 610.15, "formattedStart": "10:08", "formattedEnd": "10:10", "text": "think that's kind of the first thing I I"}, {"id": 300, "start": 610.16, "end": 612.389, "formattedStart": "10:10", "formattedEnd": "10:12", "text": "have to say. I went from a servicebased"}, {"id": 301, "start": 612.399, "end": 615.19, "formattedStart": "10:12", "formattedEnd": "10:15", "text": "business into into SAS and it just"}, {"id": 302, "start": 615.2, "end": 617.59, "formattedStart": "10:15", "formattedEnd": "10:17", "text": "allows you to make more money while"}, {"id": 303, "start": 617.6, "end": 619.11, "formattedStart": "10:17", "formattedEnd": "10:19", "text": "getting your time back. And that that's"}, {"id": 304, "start": 619.12, "end": 620.87, "formattedStart": "10:19", "formattedEnd": "10:20", "text": "huge. With a properly running system,"}, {"id": 305, "start": 620.88, "end": 622.47, "formattedStart": "10:20", "formattedEnd": "10:22", "text": "your business will make money for you"}, {"id": 306, "start": 622.48, "end": 623.99, "formattedStart": "10:22", "formattedEnd": "10:23", "text": "while you sleep. That doesn't mean it's"}, {"id": 307, "start": 624, "end": 625.59, "formattedStart": "10:24", "formattedEnd": "10:25", "text": "hands off though while you're awake, of"}, {"id": 308, "start": 625.6, "end": 628.31, "formattedStart": "10:25", "formattedEnd": "10:28", "text": "course, but I I was just amazed by how"}, {"id": 309, "start": 628.32, "end": 629.91, "formattedStart": "10:28", "formattedEnd": "10:29", "text": "the system can really work when it"}, {"id": 310, "start": 629.92, "end": 631.75, "formattedStart": "10:29", "formattedEnd": "10:31", "text": "connects. Something else that I realized"}, {"id": 311, "start": 631.76, "end": 633.11, "formattedStart": "10:31", "formattedEnd": "10:33", "text": "a little bit maybe more on the personal"}, {"id": 312, "start": 633.12, "end": 634.949, "formattedStart": "10:33", "formattedEnd": "10:34", "text": "side is it's important to make sure that"}, {"id": 313, "start": 634.9590000000001, "end": 637.03, "formattedStart": "10:34", "formattedEnd": "10:37", "text": "what you're pursuing is for the love of"}, {"id": 314, "start": 637.04, "end": 639.19, "formattedStart": "10:37", "formattedEnd": "10:39", "text": "doing it because when times get hard it"}, {"id": 315, "start": 639.2, "end": 641.11, "formattedStart": "10:39", "formattedEnd": "10:41", "text": "it's easy to quit. It also gives us a"}, {"id": 316, "start": 641.12, "end": 642.949, "formattedStart": "10:41", "formattedEnd": "10:42", "text": "competitive advantage because <PERSON> and"}, {"id": 317, "start": 642.9590000000001, "end": 644.79, "formattedStart": "10:42", "formattedEnd": "10:44", "text": "I truly love doing this business and"}, {"id": 318, "start": 644.8, "end": 646.23, "formattedStart": "10:44", "formattedEnd": "10:46", "text": "we're obsessed with it. We're always"}, {"id": 319, "start": 646.24, "end": 647.75, "formattedStart": "10:46", "formattedEnd": "10:47", "text": "thinking about it. We're always texting"}, {"id": 320, "start": 647.76, "end": 649.67, "formattedStart": "10:47", "formattedEnd": "10:49", "text": "each other about the business and if"}, {"id": 321, "start": 649.68, "end": 651.509, "formattedStart": "10:49", "formattedEnd": "10:51", "text": "it's not truly something you love,"}, {"id": 322, "start": 651.519, "end": 653.11, "formattedStart": "10:51", "formattedEnd": "10:53", "text": "someone who loves doing it, they'll"}, {"id": 323, "start": 653.12, "end": 654.87, "formattedStart": "10:53", "formattedEnd": "10:54", "text": "always surpass you. You have this"}, {"id": 324, "start": 654.88, "end": 656.87, "formattedStart": "10:54", "formattedEnd": "10:56", "text": "successful business. You get to sit down"}, {"id": 325, "start": 656.88, "end": 657.91, "formattedStart": "10:56", "formattedEnd": "10:57", "text": "and work on it every day. You're"}, {"id": 326, "start": 657.92, "end": 659.509, "formattedStart": "10:57", "formattedEnd": "10:59", "text": "passionate about it. What does an actual"}, {"id": 327, "start": 659.519, "end": 661.59, "formattedStart": "10:59", "formattedEnd": "11:01", "text": "day in the life look like for you? You"}, {"id": 328, "start": 661.6, "end": 662.71, "formattedStart": "11:01", "formattedEnd": "11:02", "text": "have a million- dollar business. What"}, {"id": 329, "start": 662.72, "end": 664.389, "formattedStart": "11:02", "formattedEnd": "11:04", "text": "does that look like? Every day it kind"}, {"id": 330, "start": 664.399, "end": 666.47, "formattedStart": "11:04", "formattedEnd": "11:06", "text": "of sits into two different buckets. I'm"}, {"id": 331, "start": 666.48, "end": 668.389, "formattedStart": "11:06", "formattedEnd": "11:08", "text": "either looking into ways to cut costs,"}, {"id": 332, "start": 668.399, "end": 670.23, "formattedStart": "11:08", "formattedEnd": "11:10", "text": "make things more efficient, improve the"}, {"id": 333, "start": 670.24, "end": 672.47, "formattedStart": "11:10", "formattedEnd": "11:12", "text": "product, or bring on completely new"}, {"id": 334, "start": 672.48, "end": 673.75, "formattedStart": "11:12", "formattedEnd": "11:13", "text": "features, integrations, and"}, {"id": 335, "start": 673.76, "end": 675.269, "formattedStart": "11:13", "formattedEnd": "11:15", "text": "partnerships. That's kind of what a day"}, {"id": 336, "start": 675.279, "end": 676.63, "formattedStart": "11:15", "formattedEnd": "11:16", "text": "in the life looks like is just constant"}, {"id": 337, "start": 676.64, "end": 678.389, "formattedStart": "11:16", "formattedEnd": "11:18", "text": "optimizations. I'm still doing bug"}, {"id": 338, "start": 678.399, "end": 680.15, "formattedStart": "11:18", "formattedEnd": "11:20", "text": "reports, too. So, there's of course the"}, {"id": 339, "start": 680.16, "end": 681.99, "formattedStart": "11:20", "formattedEnd": "11:21", "text": "mundane side of things as well. For me,"}, {"id": 340, "start": 682, "end": 684.15, "formattedStart": "11:22", "formattedEnd": "11:24", "text": "it's more of a balancing act. It's just"}, {"id": 341, "start": 684.16, "end": 686.15, "formattedStart": "11:24", "formattedEnd": "11:26", "text": "finding new ways to grow while keeping"}, {"id": 342, "start": 686.16, "end": 688.069, "formattedStart": "11:26", "formattedEnd": "11:28", "text": "our existing channels running smoothly."}, {"id": 343, "start": 688.079, "end": 690.15, "formattedStart": "11:28", "formattedEnd": "11:30", "text": "So, you know, creative strategy, working"}, {"id": 344, "start": 690.16, "end": 691.99, "formattedStart": "11:30", "formattedEnd": "11:31", "text": "with our new team, as <PERSON> said,"}, {"id": 345, "start": 692, "end": 693.99, "formattedStart": "11:32", "formattedEnd": "11:33", "text": "managing partnerships. So, it's just"}, {"id": 346, "start": 694, "end": 696.069, "formattedStart": "11:34", "formattedEnd": "11:36", "text": "figuring out how to scale while also not"}, {"id": 347, "start": 696.079, "end": 697.91, "formattedStart": "11:36", "formattedEnd": "11:37", "text": "breaking what's already working. Thanks"}, {"id": 348, "start": 697.92, "end": 699.269, "formattedStart": "11:37", "formattedEnd": "11:39", "text": "for sharing that. Last question we ask"}, {"id": 349, "start": 699.279, "end": 700.63, "formattedStart": "11:39", "formattedEnd": "11:40", "text": "all founders is if you can stand on"}, {"id": 350, "start": 700.64, "end": 702.15, "formattedStart": "11:40", "formattedEnd": "11:42", "text": "<PERSON> and <PERSON>'s shoulders when you're"}, {"id": 351, "start": 702.16, "end": 703.75, "formattedStart": "11:42", "formattedEnd": "11:43", "text": "just starting out before the success,"}, {"id": 352, "start": 703.76, "end": 704.87, "formattedStart": "11:43", "formattedEnd": "11:44", "text": "what would you tell them? There's"}, {"id": 353, "start": 704.88, "end": 706.069, "formattedStart": "11:44", "formattedEnd": "11:46", "text": "there's three things that come to mind"}, {"id": 354, "start": 706.079, "end": 708.55, "formattedStart": "11:46", "formattedEnd": "11:48", "text": "for me. The the first is build from day"}, {"id": 355, "start": 708.56, "end": 710.31, "formattedStart": "11:48", "formattedEnd": "11:50", "text": "zero optimizing for the best case"}, {"id": 356, "start": 710.32, "end": 712.55, "formattedStart": "11:50", "formattedEnd": "11:52", "text": "scenario. If a 100, 000 people signed up"}, {"id": 357, "start": 712.56, "end": 714.31, "formattedStart": "11:52", "formattedEnd": "11:54", "text": "today, could your app handle it? Or"}, {"id": 358, "start": 714.32, "end": 715.75, "formattedStart": "11:54", "formattedEnd": "11:55", "text": "would you have to rethink your pricing"}, {"id": 359, "start": 715.76, "end": 717.91, "formattedStart": "11:55", "formattedEnd": "11:57", "text": "model and your technical debt when that"}, {"id": 360, "start": 717.92, "end": 720.15, "formattedStart": "11:57", "formattedEnd": "12:00", "text": "starts to happen? The other thing is to"}, {"id": 361, "start": 720.16, "end": 721.75, "formattedStart": "12:00", "formattedEnd": "12:01", "text": "not get too attached. If I didn't go"}, {"id": 362, "start": 721.76, "end": 723.67, "formattedStart": "12:01", "formattedEnd": "12:03", "text": "through six ideas before <PERSON><PERSON>, I"}, {"id": 363, "start": 723.68, "end": 724.63, "formattedStart": "12:03", "formattedEnd": "12:04", "text": "probably still would have been trying to"}, {"id": 364, "start": 724.64, "end": 727.03, "formattedStart": "12:04", "formattedEnd": "12:07", "text": "make that first idea work, and it just"}, {"id": 365, "start": 727.04, "end": 728.629, "formattedStart": "12:07", "formattedEnd": "12:08", "text": "may not have worked at all. Maybe the"}, {"id": 366, "start": 728.639, "end": 730.23, "formattedStart": "12:08", "formattedEnd": "12:10", "text": "idea wasn't good enough. Failing fast is"}, {"id": 367, "start": 730.24, "end": 732.629, "formattedStart": "12:10", "formattedEnd": "12:12", "text": "a win-win. It there's no lose scenario"}, {"id": 368, "start": 732.639, "end": 734.069, "formattedStart": "12:12", "formattedEnd": "12:14", "text": "with that. The third thing that comes to"}, {"id": 369, "start": 734.079, "end": 736.23, "formattedStart": "12:14", "formattedEnd": "12:16", "text": "mind is to stay bootstrapped cuz staying"}, {"id": 370, "start": 736.24, "end": 738.23, "formattedStart": "12:16", "formattedEnd": "12:18", "text": "bootstrapped forces you to build lean"}, {"id": 371, "start": 738.24, "end": 740.23, "formattedStart": "12:18", "formattedEnd": "12:20", "text": "and and really focus on your value. If"}, {"id": 372, "start": 740.24, "end": 741.99, "formattedStart": "12:20", "formattedEnd": "12:21", "text": "your idea is valuable to others, then"}, {"id": 373, "start": 742, "end": 744.069, "formattedStart": "12:22", "formattedEnd": "12:24", "text": "you can scale healthfully. And if it's"}, {"id": 374, "start": 744.079, "end": 746.069, "formattedStart": "12:24", "formattedEnd": "12:26", "text": "not, then you can move on and adapt"}, {"id": 375, "start": 746.079, "end": 748.23, "formattedStart": "12:26", "formattedEnd": "12:28", "text": "quickly. You have options. So, I've been"}, {"id": 376, "start": 748.24, "end": 749.91, "formattedStart": "12:28", "formattedEnd": "12:29", "text": "doing this since I've been young. I've"}, {"id": 377, "start": 749.92, "end": 751.75, "formattedStart": "12:29", "formattedEnd": "12:31", "text": "always built things. But don't watch"}, {"id": 378, "start": 751.76, "end": 753.67, "formattedStart": "12:31", "formattedEnd": "12:33", "text": "generic tutorials. Just pick a project"}, {"id": 379, "start": 753.68, "end": 755.67, "formattedStart": "12:33", "formattedEnd": "12:35", "text": "that you're genuinely excited about and"}, {"id": 380, "start": 755.68, "end": 757.43, "formattedStart": "12:35", "formattedEnd": "12:37", "text": "then learn everything you need to make"}, {"id": 381, "start": 757.44, "end": 759.509, "formattedStart": "12:37", "formattedEnd": "12:39", "text": "that project real. You'll learn, you"}, {"id": 382, "start": 759.519, "end": 761.269, "formattedStart": "12:39", "formattedEnd": "12:41", "text": "know, 10 times faster by building"}, {"id": 383, "start": 761.279, "end": 762.87, "formattedStart": "12:41", "formattedEnd": "12:42", "text": "something you actually care about. And"}, {"id": 384, "start": 762.88, "end": 764.389, "formattedStart": "12:42", "formattedEnd": "12:44", "text": "also, you can leverage your existing"}, {"id": 385, "start": 764.399, "end": 766.47, "formattedStart": "12:44", "formattedEnd": "12:46", "text": "skills that you think might not apply to"}, {"id": 386, "start": 766.48, "end": 767.99, "formattedStart": "12:46", "formattedEnd": "12:47", "text": "building a business. I don't think our"}, {"id": 387, "start": 768, "end": 770.069, "formattedStart": "12:48", "formattedEnd": "12:50", "text": "marketing would have been as strong if I"}, {"id": 388, "start": 770.079, "end": 771.99, "formattedStart": "12:50", "formattedEnd": "12:51", "text": "didn't have a storytelling background."}, {"id": 389, "start": 772, "end": 773.43, "formattedStart": "12:52", "formattedEnd": "12:53", "text": "And I don't think that, you know, a lot"}, {"id": 390, "start": 773.44, "end": 775.43, "formattedStart": "12:53", "formattedEnd": "12:55", "text": "of what <PERSON> did, you know, come to"}, {"id": 391, "start": 775.44, "end": 777.67, "formattedStart": "12:55", "formattedEnd": "12:57", "text": "fruition if he didn't have his"}, {"id": 392, "start": 777.68, "end": 779.59, "formattedStart": "12:57", "formattedEnd": "12:59", "text": "background in the music industry and the"}, {"id": 393, "start": 779.6, "end": 781.91, "formattedStart": "12:59", "formattedEnd": "13:01", "text": "advertising industry. Thank you, <PERSON>."}, {"id": 394, "start": 781.92, "end": 783.67, "formattedStart": "13:01", "formattedEnd": "13:03", "text": "Thank you, <PERSON>, for coming on. The"}, {"id": 395, "start": 783.68, "end": 785.59, "formattedStart": "13:03", "formattedEnd": "13:05", "text": "business you guys built is amazing. I'm"}, {"id": 396, "start": 785.6, "end": 787.11, "formattedStart": "13:05", "formattedEnd": "13:07", "text": "sure it's going to keep growing. The"}, {"id": 397, "start": 787.12, "end": 788.949, "formattedStart": "13:07", "formattedEnd": "13:08", "text": "fact you built it on Bubble is also"}, {"id": 398, "start": 788.959, "end": 790.55, "formattedStart": "13:08", "formattedEnd": "13:10", "text": "amazing. I haven't heard of that before,"}, {"id": 399, "start": 790.56, "end": 791.99, "formattedStart": "13:10", "formattedEnd": "13:11", "text": "but I think that's awesome. Thanks for"}, {"id": 400, "start": 792, "end": 793.829, "formattedStart": "13:12", "formattedEnd": "13:13", "text": "coming on <PERSON><PERSON> and have a great"}, {"id": 401, "start": 793.839, "end": 795.91, "formattedStart": "13:13", "formattedEnd": "13:15", "text": "day. Thank you so much for having us."}, {"id": 402, "start": 795.92, "end": 798.31, "formattedStart": "13:15", "formattedEnd": "13:18", "text": "Thank you. I love <PERSON>'s story because"}, {"id": 403, "start": 798.32, "end": 800.71, "formattedStart": "13:18", "formattedEnd": "13:20", "text": "he went from having basically no coding"}, {"id": 404, "start": 800.72, "end": 803.03, "formattedStart": "13:20", "formattedEnd": "13:23", "text": "experience to building a million-dollar"}, {"id": 405, "start": 803.04, "end": 805.829, "formattedStart": "13:23", "formattedEnd": "13:25", "text": "app. It's unreal to see how these AI"}, {"id": 406, "start": 805.8389999999999, "end": 807.99, "formattedStart": "13:25", "formattedEnd": "13:27", "text": "tools are turning everyone into a"}, {"id": 407, "start": 808, "end": 810.069, "formattedStart": "13:28", "formattedEnd": "13:30", "text": "builder and how they're getting better"}, {"id": 408, "start": 810.079, "end": 812.069, "formattedStart": "13:30", "formattedEnd": "13:32", "text": "every single day. This is why we've been"}, {"id": 409, "start": 812.079, "end": 813.91, "formattedStart": "13:32", "formattedEnd": "13:33", "text": "working on something called Starter"}, {"id": 410, "start": 813.92, "end": 816.629, "formattedStart": "13:33", "formattedEnd": "13:36", "text": "Story Build. It's the place to learn"}, {"id": 411, "start": 816.639, "end": 819.19, "formattedStart": "13:36", "formattedEnd": "13:39", "text": "about how to build with AI and how to"}, {"id": 412, "start": 819.2, "end": 822.629, "formattedStart": "13:39", "formattedEnd": "13:42", "text": "turn your idea into a simple working"}, {"id": 413, "start": 822.639, "end": 825.19, "formattedStart": "13:42", "formattedEnd": "13:45", "text": "production app that gets users and"}, {"id": 414, "start": 825.2, "end": 827.269, "formattedStart": "13:45", "formattedEnd": "13:47", "text": "potentially can make money. In just 12"}, {"id": 415, "start": 827.279, "end": 829.509, "formattedStart": "13:47", "formattedEnd": "13:49", "text": "days, we'll guide you through the basics"}, {"id": 416, "start": 829.519, "end": 831.99, "formattedStart": "13:49", "formattedEnd": "13:51", "text": "of building with AI and you'll get the"}, {"id": 417, "start": 832, "end": 834.389, "formattedStart": "13:52", "formattedEnd": "13:54", "text": "skills to bring your ideas to life. If"}, {"id": 418, "start": 834.399, "end": 835.59, "formattedStart": "13:54", "formattedEnd": "13:55", "text": "you're interested in checking out"}, {"id": 419, "start": 835.6, "end": 837.91, "formattedStart": "13:55", "formattedEnd": "13:57", "text": "Starter Story Build, check the link in"}, {"id": 420, "start": 837.92, "end": 839.59, "formattedStart": "13:57", "formattedEnd": "13:59", "text": "the description to learn more. Thank you"}, {"id": 421, "start": 839.6, "end": 841.43, "formattedStart": "13:59", "formattedEnd": "14:01", "text": "guys again for watching. I'll see you in"}, {"id": 422, "start": 841.44, "end": 845.32, "formattedStart": "14:01", "formattedEnd": "14:05", "text": "the next one. Peace."}], "isAutoGenerated": false}