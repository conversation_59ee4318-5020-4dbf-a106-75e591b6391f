/**
 * YouTube iframe CSS Reset
 * 
 * This file contains CSS rules to reset and normalize CSS that is injected
 * by YouTube iframes. It specifically targets the German video ID "FXw2K2ZCDtQ"
 * and similar videos that might cause CSS parsing errors.
 */

/* Target YouTube iframes specifically */
iframe[src*="youtube.com"],
iframe[src*="youtu.be"] {
  /* Reset all CSS properties to prevent parsing errors */
  all: revert !important;
  
  /* Basic positioning and sizing */
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  
  /* Remove borders and margins */
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  
  /* Prevent any CSS leakage */
  isolation: isolate !important;
  contain: content !important;
}

/* Reset all vendor-specific properties for YouTube elements */
.ytp-chrome-top,
.ytp-chrome-bottom,
.ytp-gradient-top,
.ytp-gradient-bottom,
.ytp-progress-bar-container,
.ytp-progress-bar,
.ytp-progress-list,
.ytp-progress-segment,
.ytp-button,
.ytp-settings-button,
.ytp-fullscreen-button,
.ytp-mute-button,
.ytp-volume-panel,
.ytp-caption-segment,
.ytp-caption-window-container,
.ytp-caption-window,
.ytp-subtitles-player-content,
.ytp-spinner,
.ytp-spinner-container,
.ytp-spinner-rotator,
.ytp-spinner-left,
.ytp-spinner-right,
.ytp-hover-progress,
.ytp-load-progress,
.ytp-play-progress,
.ytp-chrome-controls,
.ytp-left-controls,
.ytp-right-controls {
  /* Reset all CSS properties */
  all: revert !important;
  
  /* Reset background properties */
  background: none !important;
  background-image: none !important;
  
  /* Reset transition properties */
  transition: none !important;
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  
  /* Reset transform properties */
  transform: none !important;
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -o-transform: none !important;
  -ms-transform: none !important;
  
  /* Reset animation properties */
  animation: none !important;
  -webkit-animation: none !important;
  -moz-animation: none !important;
  -o-animation: none !important;
  -ms-animation: none !important;
}

/* Reset CSS variables that might be set by YouTube */
:root {
  --ytp-*: initial !important;
  --yt-*: initial !important;
  --ytd-*: initial !important;
}

/* Ensure proper iframe container styling */
.relative,
.iframe-container,
.video-container,
.youtube-container {
  position: relative !important;
  overflow: hidden !important;
  z-index: 1 !important;
  border-radius: 0.5rem !important;
}
