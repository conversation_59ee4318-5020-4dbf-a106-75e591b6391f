/**
 * YouTube iframe CSS fixes
 *
 * This file contains comprehensive CSS fixes for YouTube iframe CSS parsing errors.
 * These errors occur because the YouTube iframe injects CSS that uses vendor-specific
 * properties that are not properly recognized by all browsers.
 *
 * This enhanced version addresses all known vendor-specific CSS property errors
 * while maintaining compatibility with different browsers.
 */

/* Global fixes for YouTube iframe CSS errors */
iframe[src*="youtube.com"],
iframe[src*="youtube-nocookie.com"] {
  /* Reset all background properties to prevent CSS parsing errors */
  background: none !important;
  background-image: none !important;
  background-position: initial !important;
  background-position-x: initial !important;
  background-position-y: initial !important;
  background-size: initial !important;
  background-repeat: initial !important;
  background-attachment: initial !important;
  background-origin: initial !important;
  background-clip: initial !important;
  background-color: transparent !important;

  /* Reset other potentially problematic properties */
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;

  /* Ensure proper sizing and positioning */
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;

  /* Fix for vendor-specific properties */
  -webkit-background-size: initial !important;
  -moz-background-size: initial !important;
  -o-background-size: initial !important;

  -webkit-background-position: initial !important;
  -moz-background-position: initial !important;
  -o-background-position: initial !important;

  /* Fix for transitions */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;

  /* Fix for transforms */
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -o-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;

  /* Fix for animations */
  -webkit-animation: none !important;
  -moz-animation: none !important;
  -o-animation: none !important;
  -ms-animation: none !important;
  animation: none !important;

  /* Fix for filters */
  -webkit-filter: none !important;
  -moz-filter: none !important;
  -o-filter: none !important;
  -ms-filter: none !important;
  filter: none !important;

  /* Fix for high contrast mode */
  -ms-high-contrast-adjust: none !important;

  /* Fix for font smoothing */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Fix for YouTube player elements */
.ytp-chrome-top,
.ytp-chrome-bottom,
.ytp-gradient-top,
.ytp-gradient-bottom,
.ytp-progress-bar-container,
.ytp-progress-bar,
.ytp-progress-list,
.ytp-progress-segment {
  background: none !important;
  background-image: none !important;
  background-position: initial !important;
  background-position-x: initial !important;
  background-position-y: initial !important;
  background-size: initial !important;

  /* Additional fixes for player elements */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;

  -webkit-transform: none !important;
  -moz-transform: none !important;
  -o-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
}

/* Fix for YouTube player buttons */
.ytp-button,
.ytp-settings-button,
.ytp-fullscreen-button,
.ytp-mute-button,
.ytp-volume-panel {
  background: none !important;
  background-image: none !important;
  background-position: initial !important;
  background-position-x: initial !important;
  background-position-y: initial !important;
  background-size: initial !important;

  /* Additional fixes for player buttons */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
}

/* Fix for YouTube player text elements */
.ytp-caption-segment,
.ytp-caption-window-container,
.ytp-caption-window,
.ytp-subtitles-player-content {
  /* Fix for text-related properties */
  -webkit-text-size-adjust: 100% !important;
  -moz-text-size-adjust: 100% !important;
  -ms-text-size-adjust: 100% !important;
  text-size-adjust: 100% !important;

  /* Fix for font smoothing */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Fix for YouTube player overlay elements */
.ytp-spinner,
.ytp-spinner-container,
.ytp-spinner-rotator,
.ytp-spinner-left,
.ytp-spinner-right {
  /* Reset animation properties */
  -webkit-animation: none !important;
  -moz-animation: none !important;
  -o-animation: none !important;
  -ms-animation: none !important;
  animation: none !important;

  /* Reset transform properties */
  -webkit-transform: none !important;
  -moz-transform: none !important;
  -o-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;
}

/* Fix for YouTube player progress bar */
.ytp-progress-bar-container,
.ytp-progress-bar,
.ytp-progress-list,
.ytp-progress-segment,
.ytp-hover-progress,
.ytp-load-progress,
.ytp-play-progress {
  /* Reset transition properties */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
}

/* Fix for YouTube player controls */
.ytp-chrome-controls,
.ytp-left-controls,
.ytp-right-controls {
  /* Reset transition properties */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
}
