/**
 * Iframe CSS Sanitizer
 * 
 * This file contains CSS rules to sanitize and normalize CSS that might be injected
 * by iframes, particularly YouTube iframes. It addresses vendor-specific CSS properties
 * that might cause parsing errors in different browsers.
 */

/* Target all iframes to prevent CSS leakage */
iframe {
  /* Isolate the iframe's CSS from affecting the parent document */
  isolation: isolate !important;
  
  /* Ensure proper containment */
  contain: content !important;
  
  /* Prevent any potential CSS conflicts with vendor prefixes */
  all: initial !important;
  
  /* Reset basic properties */
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  
  /* Ensure proper sizing */
  width: 100% !important;
  height: 100% !important;
  
  /* Ensure proper positioning */
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* Sanitize any potential CSS variables that might leak */
:root {
  /* Reset any CSS variables that might be set by iframes */
  --ytp-*: initial !important;
  --yt-*: initial !important;
}

/* Prevent iframe content from affecting parent document */
iframe::before,
iframe::after {
  content: none !important;
  display: none !important;
}

/* Sanitize specific YouTube iframe elements that might cause CSS parsing errors */
.ytp-*,
.yt-* {
  /* Reset all potentially problematic properties */
  all: initial !important;
  
  /* Ensure proper containment */
  contain: content !important;
  
  /* Reset vendor-specific properties */
  -webkit-*: initial !important;
  -moz-*: initial !important;
  -ms-*: initial !important;
  -o-*: initial !important;
}

/* Ensure proper iframe container styling */
.iframe-container,
.video-container,
.youtube-container,
[class*="iframe"],
[class*="video"],
[class*="youtube"] {
  /* Ensure proper positioning */
  position: relative !important;
  
  /* Ensure proper overflow handling */
  overflow: hidden !important;
  
  /* Ensure proper z-index */
  z-index: 1 !important;
}
