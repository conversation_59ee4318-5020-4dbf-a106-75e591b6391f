<template>
  <div>
    <div v-if="loading" class="flex justify-center py-8">
      <span class="loading loading-spinner loading-lg text-primary"></span>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>{{ error }}</span>
    </div>

    <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
      <span>No transcript found for this video.</span>
    </div>

    <div v-else class="space-y-4">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-bold">Video Transcript</h3>
        <div class="tooltip" data-tip="Click on timestamps to navigate to specific points in the video">
          <button class="btn btn-circle btn-ghost btn-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          </button>
        </div>
      </div>

      <div class="flex flex-wrap gap-2 items-center">
        <div class="join flex-1">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search in transcript..."
            @input="highlightSearchResults"
            class="input input-bordered join-item w-full"
          />
          <button
            v-if="searchResults.length > 0"
            class="btn join-item"
            @click="navigateToPrevResult"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" /></svg>
          </button>
          <button
            v-if="searchResults.length > 0"
            class="btn join-item"
            @click="navigateToNextResult"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
          </button>
        </div>

        <div class="join">
          <button
            class="btn join-item btn-sm"
            @click="decreaseFontSize"
            :disabled="fontSize <= 12"
          >
            A-
          </button>
          <button
            class="btn join-item btn-sm"
            @click="increaseFontSize"
            :disabled="fontSize >= 24"
          >
            A+
          </button>
        </div>

        <span v-if="searchResults.length > 0" class="badge badge-primary">
          {{ searchResultIndex + 1 }}/{{ searchResults.length }}
        </span>
      </div>

      <div
        class="h-[500px] overflow-y-auto bg-base-100 p-4 rounded-lg border border-base-300 relative z-30"
        ref="transcriptContent"
        :style="{ fontSize: `${fontSize}px` }"
      >
        <div
          v-for="(item, index) in transcript"
          :key="item.id"
          :id="`transcript-item-${item.id}`"
          class="mb-6 transition-all duration-200"
          :class="{ 'bg-primary/5 rounded-md p-2': currentActiveIndex === index }"
        >
          <div class="flex items-center mb-1">
            <div class="tooltip tooltip-primary" data-tip="Click to play from this timestamp">
              <button
                class="timestamp-btn flex items-center gap-1 px-3 py-1 rounded-md bg-primary/10 hover:bg-primary hover:text-white transition-all duration-200 transform hover:scale-105"
                @click="(event) => handleTimestampClick(item.start, event)"
              >
                <span class="play-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                  </svg>
                </span>
                {{ item.formattedStart || formatTime(item.start) }}
              </button>
            </div>
          </div>
          <p class="text-base-content">
            {{ item.text }}
          </p>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import api from '../services/api';
import axios from 'axios';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  },
  currentTime: {
    type: Number,
    default: 0
  }
});

const emit = defineEmits(['seek-to-time']);

const loading = ref(false);
const error = ref('');
const transcript = ref([]);
const fontSize = ref(16);
const searchQuery = ref('');
const searchResults = ref([]);
const searchResultIndex = ref(0);
const currentActiveIndex = ref(-1);
const transcriptContent = ref(null);

// Fetch transcript when component mounts or videoId changes
watch(() => props.videoId, fetchTranscript, { immediate: true });

// Watch for currentTime changes to highlight the current transcript item
watch(() => props.currentTime, (newTime) => {
  if (!transcript.value || transcript.value.length === 0) return;

  // Find the transcript item that corresponds to the current time
  const activeIndex = transcript.value.findIndex(
    item => newTime >= item.start && newTime < item.end
  );

  if (activeIndex !== -1 && activeIndex !== currentActiveIndex.value) {
    currentActiveIndex.value = activeIndex;
    scrollToActiveItem();
  }
});

async function fetchTranscript() {
  if (!props.videoId) {
    console.log('No videoId provided, skipping transcript fetch');
    return;
  }

  loading.value = true;
  error.value = '';
  transcript.value = [];

  try {
    console.log('Fetching transcript for video ID:', props.videoId);

    // Use direct fetch instead of axios or api service
    const response = await fetch(`http://localhost:3000/api/transcript/${props.videoId}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('Transcript API response:', data);

    if (data && data.transcript && Array.isArray(data.transcript)) {
      console.log('Transcript data found, length:', data.transcript.length);
      transcript.value = data.transcript;

      // Calculate end times for each transcript item
      for (let i = 0; i < transcript.value.length; i++) {
        const item = transcript.value[i];
        const nextItem = transcript.value[i + 1];

        // If there's a next item, use its start time as the end time
        // Otherwise, add a default duration (e.g., 5 seconds)
        item.end = nextItem ? nextItem.start : item.start + 5;
      }

      console.log('Processed transcript:', transcript.value);
    } else {
      console.error('Invalid transcript data format:', data);
      error.value = 'Invalid transcript data format';
    }
  } catch (err) {
    console.error('Error fetching transcript:', err);
    console.error('Error details:', err.message);
    error.value = 'Failed to fetch transcript: ' + err.message;
  } finally {
    loading.value = false;
    console.log('Transcript fetch completed. Has transcript:', transcript.value.length > 0);
  }
}

function seekToTime(time) {
  emit('seek-to-time', time);
}

function handleTimestampClick(time, event) {
  console.log('TranscriptViewer: Timestamp clicked, seeking to time:', time);

  // Add a small delay to ensure the player is ready
  setTimeout(() => {
    seekToTime(time);
  }, 100);

  // Provide visual feedback
  const buttons = document.querySelectorAll('.timestamp-btn');
  buttons.forEach(btn => {
    btn.classList.remove('clicked');
    btn.classList.remove('bg-primary');
    btn.classList.remove('text-white');
    btn.classList.add('bg-primary/10');
  });

  // Add a 'clicked' class to the button that was clicked
  if (event && event.currentTarget) {
    event.currentTarget.classList.add('clicked');
    event.currentTarget.classList.add('bg-primary');
    event.currentTarget.classList.add('text-white');
    event.currentTarget.classList.remove('bg-primary/10');

    // Add a pulse animation
    event.currentTarget.animate(
      [
        { transform: 'scale(1)' },
        { transform: 'scale(1.1)' },
        { transform: 'scale(1)' }
      ],
      {
        duration: 300,
        easing: 'ease-in-out'
      }
    );
  }
}

function splitTextIntoWords(text) {
  return text.split(/\s+/);
}

function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [
    hours.toString().padStart(2, '0'),
    minutes.toString().padStart(2, '0'),
    secs.toString().padStart(2, '0')
  ].join(':');
}

function increaseFontSize() {
  if (fontSize.value < 24) {
    fontSize.value += 2;
  }
}

function decreaseFontSize() {
  if (fontSize.value > 12) {
    fontSize.value -= 2;
  }
}

function highlightSearchResults() {
  if (!searchQuery.value.trim() || !transcript.value.length) {
    searchResults.value = [];
    searchResultIndex.value = 0;
    return;
  }

  const query = searchQuery.value.toLowerCase();
  const results = [];

  transcript.value.forEach((item) => {
    const text = item.text.toLowerCase();
    let index = text.indexOf(query);

    while (index !== -1) {
      // Calculate which word this match corresponds to
      const textBeforeMatch = text.substring(0, index);
      const wordIndex = textBeforeMatch.split(/\s+/).length - 1;

      results.push({
        itemId: item.id,
        wordIndex,
        time: item.start
      });

      index = text.indexOf(query, index + 1);
    }
  });

  searchResults.value = results;
  searchResultIndex.value = 0;

  if (results.length > 0) {
    navigateToResult(0);
  }
}

function navigateToNextResult() {
  if (searchResults.value.length === 0) return;

  searchResultIndex.value = (searchResultIndex.value + 1) % searchResults.value.length;
  navigateToResult(searchResultIndex.value);
}

function navigateToPrevResult() {
  if (searchResults.value.length === 0) return;

  searchResultIndex.value = (searchResultIndex.value - 1 + searchResults.value.length) % searchResults.value.length;
  navigateToResult(searchResultIndex.value);
}

function navigateToResult(index) {
  const result = searchResults.value[index];
  if (!result) return;

  // Scroll to the item
  const itemElement = document.getElementById(`transcript-item-${result.itemId}`);
  if (itemElement) {
    itemElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }

  // Optionally seek to the time
  seekToTime(result.time);
}

function isWordHighlighted(itemId, wordIndex) {
  if (!searchQuery.value.trim() || searchResults.value.length === 0) return false;

  const currentResult = searchResults.value[searchResultIndex.value];
  return currentResult && currentResult.itemId === itemId && currentResult.wordIndex === wordIndex;
}

function scrollToActiveItem() {
  if (currentActiveIndex.value === -1 || !transcript.value.length) return;

  // Use nextTick to ensure the DOM has updated with the active class
  nextTick(() => {
    const activeItem = transcript.value[currentActiveIndex.value];
    const itemElement = document.getElementById(`transcript-item-${activeItem.id}`);

    if (itemElement && transcriptContent.value) {
      // Check if the element is not visible in the viewport
      const containerRect = transcriptContent.value.getBoundingClientRect();
      const elementRect = itemElement.getBoundingClientRect();

      const isVisible = (
        elementRect.top >= containerRect.top &&
        elementRect.bottom <= containerRect.bottom
      );

      if (!isVisible) {
        // Use a small delay to ensure smooth scrolling
        setTimeout(() => {
          itemElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }, 100);
      }
    }
  });
}

async function downloadTranscript(format) {
  if (!transcript.value || transcript.value.length === 0) {
    error.value = 'No transcript available to download';
    return;
  }

  try {
    // Get the download URL
    const downloadUrl = `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}/captions/${props.videoId}/download?format=${format}`;

    // Create a temporary link and trigger the download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `youtube-transcript-${props.videoId}.${format}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (err) {
    console.error('Error downloading transcript:', err);
    error.value = 'Failed to download transcript';
  }
}
</script>

<style scoped>
.transcript-viewer {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading, .error, .no-transcript {
  text-align: center;
  padding: 20px;
}

.error {
  color: #ff0000;
}

.transcript-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

h3 {
  margin-bottom: 10px;
  color: #333;
}

.instruction {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #4CAF50;
}

.timestamp-icon-example {
  color: #4CAF50;
  font-weight: bold;
}

.transcript-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.search-box input {
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.search-results {
  font-size: 14px;
  color: #666;
}

.search-navigation {
  display: flex;
  gap: 5px;
}

.search-navigation button {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
}

.font-size-controls {
  display: flex;
  gap: 5px;
}

.font-size-controls button {
  padding: 4px 8px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
}

.transcript-content {
  background-color: white;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.transcript-item {
  margin-bottom: 12px;
  padding: 8px 8px 8px 15px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: flex-start;
}

.transcript-item.active {
  background-color: #e6f7ff;
  border-left: 4px solid #4CAF50;
  padding-left: 5px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.transcript-item.active::before {
  content: "▶";
  position: absolute;
  left: -15px;
  color: #4CAF50;
  font-size: 12px;
}

.timestamp-btn {
  display: inline-flex;
  align-items: center;
  min-width: 90px;
  color: #4CAF50;
  font-weight: bold;
  font-size: 0.9em;
  margin-right: 10px;
  cursor: pointer;
  user-select: none;
  background-color: #f0f8f0;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #d0e8d0;
  position: relative;
  z-index: 5;
  pointer-events: auto;
  text-align: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.timestamp-btn:hover {
  color: white;
  background-color: #4CAF50;
  border-color: #4CAF50;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.timestamp-btn:focus {
  outline: 2px solid #4CAF50;
  outline-offset: 2px;
}

.timestamp-btn.clicked {
  color: white;
  background-color: #4CAF50;
  border-color: #4CAF50;
  animation: pulse 0.5s;
}

.timestamp-icon {
  margin-right: 5px;
  font-size: 1.1em;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.text {
  display: inline-block;
  flex: 1;
}

.word {
  display: inline-block;
  margin-right: 4px;
  border-radius: 2px;
  padding: 0 2px;
}

.word.highlighted {
  background-color: #ffeb3b;
  font-weight: bold;
}

.download-options {
  margin-top: 20px;
}

.format-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

button {
  padding: 10px 15px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #45a049;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}
</style>
