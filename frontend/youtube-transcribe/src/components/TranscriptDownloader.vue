<template>
  <div class="w-full mt-4">
    <div class="flex items-center mb-2">
      <span class="font-medium mr-2">Get the transcript:</span>
      <div class="flex gap-2 flex-1">
        <!-- Copy Button -->
        <button 
          class="btn btn-outline flex-1" 
          @click="copyTranscript"
          :disabled="loading || !videoId"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
          </svg>
          <span v-if="!copyLoading">Copy</span>
          <span v-else class="loading loading-spinner loading-sm"></span>
        </button>

        <!-- Download Dropdown -->
        <div class="dropdown dropdown-end flex-1">
          <label 
            tabindex="0" 
            class="btn btn-primary w-full"
            :class="{ 'btn-disabled': loading || !videoId }"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            <span v-if="!downloadLoading">Download</span>
            <span v-else class="loading loading-spinner loading-sm"></span>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </label>
          
          <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
            <li><a @click="downloadTranscript('txt')" class="flex items-center gap-2">
              <span class="text-lg">📄</span>
              <div>
                <div class="font-medium">Text (.txt)</div>
                <div class="text-xs opacity-70">Plain text with timestamps</div>
              </div>
            </a></li>
            
            <li><a @click="downloadTranscript('srt')" class="flex items-center gap-2">
              <span class="text-lg">🎬</span>
              <div>
                <div class="font-medium">Subtitles (.srt)</div>
                <div class="text-xs opacity-70">Standard subtitle format</div>
              </div>
            </a></li>
            
            <li><a @click="downloadTranscript('vtt')" class="flex items-center gap-2">
              <span class="text-lg">🌐</span>
              <div>
                <div class="font-medium">WebVTT (.vtt)</div>
                <div class="text-xs opacity-70">Web video text tracks</div>
              </div>
            </a></li>
            
            <li><a @click="downloadTranscript('docx')" class="flex items-center gap-2">
              <span class="text-lg">📝</span>
              <div>
                <div class="font-medium">Word (.docx)</div>
                <div class="text-xs opacity-70">Microsoft Word document</div>
              </div>
            </a></li>
          </ul>
        </div>
      </div>
    </div>
    
    <!-- Language Info -->
    <div v-if="selectedLanguage" class="text-sm opacity-70 mt-1">
      <span class="flag-icon mr-1">{{ selectedLanguage.flag || '🌐' }}</span>
      Downloads will be in {{ selectedLanguage.name || 'selected language' }}
    </div>
    
    <!-- Success/Error Messages -->
    <div v-if="successMessage" class="alert alert-success mt-2">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ successMessage }}</span>
    </div>
    
    <div v-if="errorMessage" class="alert alert-error mt-2">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errorMessage }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  },
  selectedLanguage: {
    type: Object,
    default: () => ({ code: 'en', name: 'English', flag: '🇺🇸' })
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const copyLoading = ref(false);
const downloadLoading = ref(false);
const successMessage = ref('');
const errorMessage = ref('');

// Clear messages after 3 seconds
function showMessage(message, isError = false) {
  if (isError) {
    errorMessage.value = message;
    successMessage.value = '';
  } else {
    successMessage.value = message;
    errorMessage.value = '';
  }
  
  setTimeout(() => {
    successMessage.value = '';
    errorMessage.value = '';
  }, 3000);
}

// Function to copy transcript to clipboard
async function copyTranscript() {
  if (!props.videoId) return;
  
  copyLoading.value = true;
  
  try {
    // Fetch the transcript in the selected language
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const language = props.selectedLanguage?.code || 'en';
    const response = await fetch(`${API_URL}/transcript/${props.videoId}?lang=${language}`);
    const data = await response.json();

    if (data && data.transcript) {
      // Extract text from transcript
      const text = data.transcript.map(item => `[${item.formattedStart}] ${item.text}`).join('\n\n');

      // Copy to clipboard
      await navigator.clipboard.writeText(text);

      showMessage(`Transcript copied to clipboard! (${props.selectedLanguage?.name || 'Selected language'})`);
    } else {
      throw new Error('No transcript data received');
    }
  } catch (err) {
    console.error('Error copying transcript:', err);
    showMessage('Failed to copy transcript', true);
  } finally {
    copyLoading.value = false;
  }
}

// Function to download transcript in specified format
async function downloadTranscript(format) {
  if (!props.videoId) return;
  
  downloadLoading.value = true;
  
  try {
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const language = props.selectedLanguage?.code || 'en';
    const downloadUrl = `${API_URL}/captions/${props.videoId}/download?format=${format}&lang=${language}`;

    // Create a temporary link and trigger the download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `youtube-transcript-${props.videoId}.${format}`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    const formatNames = {
      txt: 'Text',
      srt: 'SRT Subtitles',
      vtt: 'WebVTT',
      docx: 'Word Document'
    };

    showMessage(`${formatNames[format] || format.toUpperCase()} downloaded! (${props.selectedLanguage?.name || 'Selected language'})`);
  } catch (err) {
    console.error('Error downloading transcript:', err);
    showMessage('Failed to download transcript', true);
  } finally {
    downloadLoading.value = false;
  }
}
</script>

<style scoped>
.flag-icon {
  display: inline-block;
  width: 1.2em;
  text-align: center;
}

.dropdown-content {
  min-width: 250px;
}

.dropdown-content li a {
  padding: 0.75rem 1rem;
  white-space: normal;
}

.dropdown-content li a:hover {
  background-color: var(--fallback-b2,oklch(var(--b2)/var(--tw-bg-opacity)));
}
</style>
